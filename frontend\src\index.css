/* Global styles for MarketHawk */

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  font-family: 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #0a0e1a;
  color: #ffffff;
  overflow-x: hidden;
}

code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1f2e;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

/* Loading animations */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spin {
  animation: spin 1s linear infinite;
}

/* Utility classes */
.text-green {
  color: #4caf50 !important;
}

.text-red {
  color: #f44336 !important;
}

.text-yellow {
  color: #ff9800 !important;
}

.bg-green {
  background-color: #4caf50 !important;
}

.bg-red {
  background-color: #f44336 !important;
}

.bg-yellow {
  background-color: #ff9800 !important;
}

/* Chart container styles */
.chart-container {
  position: relative;
  width: 100%;
  height: 400px;
}

.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #1a1f2e;
  border-radius: 8px;
}

/* Data grid custom styles */
.data-grid-container {
  height: 600px;
  width: 100%;
}

.data-grid-container .MuiDataGrid-root {
  border: none;
  background-color: #1a1f2e;
}

.data-grid-container .MuiDataGrid-cell {
  border-bottom: 1px solid #2d3748;
}

.data-grid-container .MuiDataGrid-columnHeaders {
  background-color: #2d3748;
  border-bottom: 1px solid #4a5568;
}

.data-grid-container .MuiDataGrid-row:hover {
  background-color: #2d3748;
}

/* Form styles */
.form-container {
  max-width: 400px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #1a1f2e;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.form-title {
  text-align: center;
  margin-bottom: 2rem;
  color: #00d4aa;
  font-size: 2rem;
  font-weight: 600;
}

/* Button styles */
.btn-primary {
  background-color: #00d4aa !important;
  color: #ffffff !important;
}

.btn-primary:hover {
  background-color: #00a37c !important;
}

.btn-secondary {
  background-color: #ff6b35 !important;
  color: #ffffff !important;
}

.btn-secondary:hover {
  background-color: #c73e0a !important;
}

/* Card styles */
.metric-card {
  background: linear-gradient(135deg, #1a1f2e 0%, #2d3748 100%);
  border: 1px solid #4a5568;
  border-radius: 12px;
  padding: 1.5rem;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.15);
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.metric-label {
  font-size: 0.875rem;
  color: #b0bec5;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-change {
  font-size: 0.875rem;
  font-weight: 600;
  margin-top: 0.5rem;
}

/* Status indicators */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-online {
  background-color: #4caf50;
}

.status-offline {
  background-color: #f44336;
}

.status-warning {
  background-color: #ff9800;
}

/* Responsive design */
@media (max-width: 768px) {
  .form-container {
    margin: 1rem;
    padding: 1.5rem;
  }

  .metric-card {
    padding: 1rem;
  }

  .metric-value {
    font-size: 1.5rem;
  }
}

/* Print styles */
@media print {
  body {
    background-color: white !important;
    color: black !important;
  }

  .no-print {
    display: none !important;
  }
}
