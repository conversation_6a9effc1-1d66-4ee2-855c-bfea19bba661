import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// Types
interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  email: string;
  username: string;
  password: string;
  first_name?: string;
  last_name?: string;
}

interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

interface User {
  id: number;
  email: string;
  username: string;
  first_name?: string;
  last_name?: string;
  is_active: boolean;
  is_verified: boolean;
  subscription_tier: string;
  created_at: string;
}

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

class APIClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // Handle 401 errors (token expired)
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const refreshToken = localStorage.getItem('refresh_token');
            if (refreshToken) {
              const response = await this.refreshAccessToken(refreshToken);
              localStorage.setItem('access_token', response.access_token);

              // Retry original request with new token
              originalRequest.headers.Authorization = `Bearer ${response.access_token}`;
              return this.client(originalRequest);
            }
          } catch (refreshError) {
            // Refresh failed, redirect to login
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            window.location.href = '/login';
          }
        }

        return Promise.reject(error);
      }
    );
  }

  private async refreshAccessToken(
    refreshToken: string
  ): Promise<LoginResponse> {
    const response = await axios.post(`${API_BASE_URL}/api/v1/auth/refresh`, {
      refresh_token: refreshToken,
    });
    return response.data;
  }

  // Generic request methods
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.get(url, config);
    return response.data;
  }

  async post<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response: AxiosResponse<T> = await this.client.post(
      url,
      data,
      config
    );
    return response.data;
  }

  async put<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response: AxiosResponse<T> = await this.client.put(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.delete(url, config);
    return response.data;
  }
}

// Create API client instance
const apiClient = new APIClient();

// Authentication API
export const authAPI = {
  login: (credentials: LoginCredentials): Promise<LoginResponse> =>
    apiClient.post('/api/v1/auth/login', credentials),

  register: (userData: RegisterData): Promise<User> =>
    apiClient.post('/api/v1/auth/register', userData),

  logout: (): Promise<void> => apiClient.post('/api/v1/auth/logout'),

  getCurrentUser: (): Promise<User> => apiClient.get('/api/v1/auth/me'),

  updateProfile: (userData: Partial<RegisterData>): Promise<User> =>
    apiClient.put('/api/v1/auth/me', userData),

  changePassword: (passwordData: {
    current_password: string;
    new_password: string;
  }): Promise<void> =>
    apiClient.post('/api/v1/auth/change-password', passwordData),

  generateApiKey: (): Promise<{ api_key: string; message: string }> =>
    apiClient.post('/api/v1/auth/generate-api-key'),

  revokeApiKey: (): Promise<{ message: string }> =>
    apiClient.delete('/api/v1/auth/revoke-api-key'),
};

// Screening API
export const screeningAPI = {
  getStrategies: (): Promise<any[]> =>
    apiClient.get('/api/v1/screening/strategies'),

  createStrategy: (strategyData: any): Promise<any> =>
    apiClient.post('/api/v1/screening/strategies', strategyData),

  updateStrategy: (id: number, strategyData: any): Promise<any> =>
    apiClient.put(`/api/v1/screening/strategies/${id}`, strategyData),

  deleteStrategy: (id: number): Promise<void> =>
    apiClient.delete(`/api/v1/screening/strategies/${id}`),

  runScreening: (strategyId: number, symbols?: string[]): Promise<any[]> =>
    apiClient.post(`/api/v1/screening/strategies/${strategyId}/run`, {
      symbols,
    }),

  getScreeningResults: (strategyId: number, limit?: number): Promise<any[]> =>
    apiClient.get(`/api/v1/screening/strategies/${strategyId}/results`, {
      params: { limit },
    }),
};

// Trading API
export const tradingAPI = {
  getTrades: (limit?: number): Promise<any[]> =>
    apiClient.get('/api/v1/trading/trades', { params: { limit } }),

  getPositions: (): Promise<any[]> =>
    apiClient.get('/api/v1/trading/positions'),

  submitOrder: (orderData: any): Promise<any> =>
    apiClient.post('/api/v1/trading/orders', orderData),

  cancelOrder: (orderId: number): Promise<void> =>
    apiClient.delete(`/api/v1/trading/orders/${orderId}`),

  getOrderStatus: (orderId: number): Promise<any> =>
    apiClient.get(`/api/v1/trading/orders/${orderId}`),
};

// Portfolio API
export const portfolioAPI = {
  getPortfolios: (): Promise<any[]> =>
    apiClient.get('/api/v1/portfolio/portfolios'),

  createPortfolio: (portfolioData: any): Promise<any> =>
    apiClient.post('/api/v1/portfolio/portfolios', portfolioData),

  updatePortfolio: (id: number, portfolioData: any): Promise<any> =>
    apiClient.put(`/api/v1/portfolio/portfolios/${id}`, portfolioData),

  deletePortfolio: (id: number): Promise<void> =>
    apiClient.delete(`/api/v1/portfolio/portfolios/${id}`),

  getPortfolioPerformance: (id: number, period?: string): Promise<any> =>
    apiClient.get(`/api/v1/portfolio/portfolios/${id}/performance`, {
      params: { period },
    }),
};

// Market Data API
export const marketDataAPI = {
  getQuote: (symbol: string): Promise<any> =>
    apiClient.get(`/api/v1/market-data/quote/${symbol}`),

  getHistoricalData: (
    symbol: string,
    period?: string,
    interval?: string
  ): Promise<any[]> =>
    apiClient.get(`/api/v1/market-data/historical/${symbol}`, {
      params: { period, interval },
    }),

  getTechnicalIndicators: (
    symbol: string,
    indicators: string[]
  ): Promise<any> =>
    apiClient.get(`/api/v1/market-data/indicators/${symbol}`, {
      params: { indicators: indicators.join(',') },
    }),

  getFundamentals: (symbol: string): Promise<any> =>
    apiClient.get(`/api/v1/market-data/fundamentals/${symbol}`),

  searchStocks: (query: string, limit?: number): Promise<any[]> =>
    apiClient.get('/api/v1/market-data/search', {
      params: { query, limit },
    }),

  getMarketStatus: (): Promise<any> =>
    apiClient.get('/api/v1/market-data/status'),
};

// Backtesting API
export const backtestingAPI = {
  runBacktest: (strategyId: number, config: any): Promise<any> =>
    apiClient.post(
      `/api/v1/backtesting/strategies/${strategyId}/backtest`,
      config
    ),

  getBacktestResults: (backtestId: number): Promise<any> =>
    apiClient.get(`/api/v1/backtesting/results/${backtestId}`),

  getBacktestHistory: (strategyId: number): Promise<any[]> =>
    apiClient.get(`/api/v1/backtesting/strategies/${strategyId}/history`),
};

// Health check
export const healthAPI = {
  getHealth: (): Promise<any> => apiClient.get('/health'),

  getDetailedHealth: (): Promise<any> => apiClient.get('/health/detailed'),
};

export default apiClient;
