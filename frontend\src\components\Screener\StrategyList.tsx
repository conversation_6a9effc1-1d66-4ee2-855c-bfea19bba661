import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  IconButton,
  Chip,
  Grid,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Skeleton,
  Tooltip,
  LinearProgress,
} from '@mui/material';
import {
  PlayArrow,
  Edit,
  Delete,
  MoreVert,
  Schedule,
  TrendingUp,
  Assessment,
  Pause,
  ContentCopy,
  Share,
  Visibility,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { motion } from 'framer-motion';

interface Strategy {
  id: number;
  name: string;
  description: string;
  screening_criteria: any[];
  universe: string[];
  min_score: number;
  max_results: number;
  is_active: boolean;
  schedule_enabled: boolean;
  schedule_frequency: string;
  created_at: string;
  updated_at: string;
  last_run: string;
  total_runs: number;
}

interface StrategyListProps {
  strategies: Strategy[];
  isLoading: boolean;
  onEdit: (strategy: Strategy) => void;
  onRun: (strategyId: number) => void;
  isRunning: boolean;
}

const StrategyList: React.FC<StrategyListProps> = ({
  strategies,
  isLoading,
  onEdit,
  onRun,
  isRunning,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(
    null
  );
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [strategyToDelete, setStrategyToDelete] = useState<Strategy | null>(
    null
  );

  const handleMenuOpen = (
    event: React.MouseEvent<HTMLElement>,
    strategy: Strategy
  ) => {
    setAnchorEl(event.currentTarget);
    setSelectedStrategy(strategy);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedStrategy(null);
  };

  const handleEdit = () => {
    if (selectedStrategy) {
      onEdit(selectedStrategy);
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    setStrategyToDelete(selectedStrategy);
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const handleConfirmDelete = () => {
    if (strategyToDelete) {
      // dispatch(deleteStrategy(strategyToDelete.id));
      console.log('Deleting strategy:', strategyToDelete.id);
    }
    setDeleteDialogOpen(false);
    setStrategyToDelete(null);
  };

  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
    setStrategyToDelete(null);
  };

  const handleDuplicate = () => {
    if (selectedStrategy) {
      // Create a copy of the strategy
      const duplicatedStrategy = {
        ...selectedStrategy,
        name: `${selectedStrategy.name} (Copy)`,
        id: 0, // Use 0 for new strategies
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        last_run: '',
        total_runs: 0,
      };
      onEdit(duplicatedStrategy);
    }
    handleMenuClose();
  };

  const getStatusColor = (strategy: Strategy) => {
    if (!strategy.is_active) return 'default';
    if (strategy.schedule_enabled) return 'success';
    return 'primary';
  };

  const getStatusText = (strategy: Strategy) => {
    if (!strategy.is_active) return 'Inactive';
    if (strategy.schedule_enabled) return 'Scheduled';
    return 'Active';
  };

  const formatLastRun = (lastRun: string) => {
    if (!lastRun) return 'Never';
    return format(new Date(lastRun), 'MMM dd, yyyy HH:mm');
  };

  if (isLoading) {
    return (
      <Grid container spacing={3}>
        {[1, 2, 3].map((index) => (
          <Grid item xs={12} md={6} lg={4} key={index}>
            <Card>
              <CardContent>
                <Skeleton variant="text" width="60%" height={32} />
                <Skeleton
                  variant="text"
                  width="100%"
                  height={20}
                  sx={{ mt: 1 }}
                />
                <Skeleton variant="text" width="80%" height={20} />
                <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                  <Skeleton variant="rectangular" width={60} height={24} />
                  <Skeleton variant="rectangular" width={80} height={24} />
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    mt: 2,
                  }}
                >
                  <Skeleton variant="rectangular" width={80} height={36} />
                  <Skeleton variant="rectangular" width={40} height={36} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  }

  if (strategies.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <TrendingUp sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h5" color="text.secondary" gutterBottom>
          No Strategies Yet
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          Create your first screening strategy to find stocks that match your
          criteria
        </Typography>
        <Button variant="contained" size="large">
          Create Strategy
        </Button>
      </Box>
    );
  }

  return (
    <>
      <Grid container spacing={3}>
        {strategies.map((strategy, index) => (
          <Grid item xs={12} md={6} lg={4} key={strategy.id}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  '&:hover': {
                    boxShadow: (theme) => theme.shadows[8],
                  },
                }}
              >
                <CardContent sx={{ flexGrow: 1 }}>
                  {/* Header */}
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'flex-start',
                      mb: 2,
                    }}
                  >
                    <Typography
                      variant="h6"
                      component="h3"
                      sx={{ fontWeight: 600 }}
                    >
                      {strategy.name}
                    </Typography>
                    <IconButton
                      size="small"
                      onClick={(e) => handleMenuOpen(e, strategy)}
                    >
                      <MoreVert />
                    </IconButton>
                  </Box>

                  {/* Description */}
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      mb: 2,
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                    }}
                  >
                    {strategy.description || 'No description provided'}
                  </Typography>

                  {/* Status and Universe */}
                  <Box
                    sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}
                  >
                    <Chip
                      label={getStatusText(strategy)}
                      color={getStatusColor(strategy)}
                      size="small"
                    />
                    {strategy.universe.map((universe) => (
                      <Chip
                        key={universe}
                        label={universe}
                        variant="outlined"
                        size="small"
                      />
                    ))}
                  </Box>

                  {/* Criteria Count */}
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mb: 1 }}
                  >
                    <strong>{strategy.screening_criteria.length}</strong>{' '}
                    criteria
                  </Typography>

                  {/* Stats */}
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      mb: 2,
                    }}
                  >
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Last Run
                      </Typography>
                      <Typography variant="body2">
                        {formatLastRun(strategy.last_run)}
                      </Typography>
                    </Box>
                    <Box sx={{ textAlign: 'right' }}>
                      <Typography variant="caption" color="text.secondary">
                        Total Runs
                      </Typography>
                      <Typography variant="body2">
                        {strategy.total_runs}
                      </Typography>
                    </Box>
                  </Box>

                  {/* Schedule Info */}
                  {strategy.schedule_enabled && (
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Schedule
                        sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }}
                      />
                      <Typography variant="caption" color="text.secondary">
                        Runs {strategy.schedule_frequency}
                      </Typography>
                    </Box>
                  )}

                  {/* Progress Bar for Running Strategy */}
                  {isRunning && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="caption" color="text.secondary">
                        Screening in progress...
                      </Typography>
                      <LinearProgress sx={{ mt: 0.5 }} />
                    </Box>
                  )}
                </CardContent>

                {/* Actions */}
                <Box sx={{ p: 2, pt: 0 }}>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="contained"
                      size="small"
                      startIcon={<PlayArrow />}
                      onClick={() => onRun(strategy.id)}
                      disabled={!strategy.is_active || isRunning}
                      sx={{ flexGrow: 1 }}
                    >
                      Run
                    </Button>
                    <Tooltip title="Edit Strategy">
                      <IconButton
                        size="small"
                        onClick={() => onEdit(strategy)}
                        color="primary"
                      >
                        <Edit />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="View Results">
                      <IconButton size="small" color="primary">
                        <Assessment />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleEdit}>
          <ListItemIcon>
            <Edit fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit</ListItemText>
        </MenuItem>

        <MenuItem onClick={handleDuplicate}>
          <ListItemIcon>
            <ContentCopy fontSize="small" />
          </ListItemIcon>
          <ListItemText>Duplicate</ListItemText>
        </MenuItem>

        <MenuItem>
          <ListItemIcon>
            <Visibility fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Results</ListItemText>
        </MenuItem>

        <MenuItem>
          <ListItemIcon>
            <Share fontSize="small" />
          </ListItemIcon>
          <ListItemText>Share</ListItemText>
        </MenuItem>

        <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
          <ListItemIcon>
            <Delete fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleCancelDelete}>
        <DialogTitle>Delete Strategy</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{strategyToDelete?.name}"? This
            action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete}>Cancel</Button>
          <Button
            onClick={handleConfirmDelete}
            color="error"
            variant="contained"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default StrategyList;
