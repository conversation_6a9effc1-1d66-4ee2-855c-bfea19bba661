"""
Advanced trading API endpoints for options, algorithmic strategies, and risk management
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from fastapi.security import HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.options import StrategyType, OptionType
from app.services.options_strategies import OptionsStrategyManager
from app.services.algorithmic_strategies import AlgorithmicStrategyManager
from app.services.advanced_risk_management import AdvancedRiskManager
from app.utils.rate_limiter import RateLimiter

router = APIRouter()
security = HTTPBearer()

# Rate limiters for advanced features
options_rate_limiter = RateLimiter(
    max_requests=20,  # Free tier: 20 options requests per hour
    time_window=3600,
    premium_max_requests=200,  # Premium tier: 200 requests per hour
    enterprise_max_requests=2000  # Enterprise tier: 2000 requests per hour
)

algo_rate_limiter = RateLimiter(
    max_requests=10,  # Free tier: 10 algo requests per hour
    time_window=3600,
    premium_max_requests=100,  # Premium tier: 100 requests per hour
    enterprise_max_requests=1000  # Enterprise tier: 1000 requests per hour
)

# Pydantic models
class OptionLeg(BaseModel):
    """Option leg configuration"""
    option_type: OptionType
    action: str = Field(..., pattern="^(buy|sell)$")
    strike_price: float = Field(..., gt=0)
    quantity: int = Field(default=1, gt=0)
    premium: Optional[float] = Field(None, ge=0)

class OptionsStrategyRequest(BaseModel):
    """Options strategy creation request"""
    underlying_symbol: str = Field(..., min_length=1, max_length=10)
    strategy_type: StrategyType
    legs: List[OptionLeg] = Field(..., min_items=1, max_items=6)
    quantity: int = Field(default=1, gt=0, le=100)
    expiration_date: Optional[datetime] = None
    broker_account_id: int

class AlgorithmicStrategyRequest(BaseModel):
    """Algorithmic strategy request"""
    strategy_type: str = Field(..., pattern="^(momentum|mean_reversion|pairs_trading)$")
    symbols: List[str] = Field(..., min_items=1, max_items=50)
    parameters: Dict[str, Any]
    broker_account_id: int

class RiskAssessmentRequest(BaseModel):
    """Risk assessment request"""
    broker_account_id: int
    include_greeks: bool = Field(default=True)
    include_correlations: bool = Field(default=True)
    include_hedging: bool = Field(default=True)

# Options Trading Endpoints
@router.post("/options/strategies", tags=["Options Trading"])
async def create_options_strategy(
    request: OptionsStrategyRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new options strategy"""
    
    # Rate limiting
    if not await options_rate_limiter.check_rate_limit(
        f"options_create_{current_user.id}", 
        current_user.subscription_tier
    ):
        raise HTTPException(status_code=429, detail="Rate limit exceeded")
    
    try:
        options_manager = OptionsStrategyManager()
        
        # Convert legs to dict format
        legs_dict = []
        for leg in request.legs:
            legs_dict.append({
                "option_type": leg.option_type.value,
                "action": leg.action,
                "strike_price": leg.strike_price,
                "quantity": leg.quantity,
                "premium": leg.premium or 0.0
            })
        
        result = await options_manager.create_options_strategy(
            user_id=current_user.id,
            broker_account_id=request.broker_account_id,
            underlying_symbol=request.underlying_symbol,
            strategy_type=request.strategy_type,
            legs=legs_dict,
            quantity=request.quantity,
            expiration_date=request.expiration_date
        )
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "strategy_id": result["strategy_id"],
            "strategy": result["strategy"],
            "risk_metrics": result["risk_metrics"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating options strategy: {str(e)}")

@router.post("/options/strategies/{strategy_id}/execute", tags=["Options Trading"])
async def execute_options_strategy(
    strategy_id: int,
    is_paper_trade: bool = Query(default=True),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Execute an options strategy"""
    
    # Rate limiting
    if not await options_rate_limiter.check_rate_limit(
        f"options_execute_{current_user.id}", 
        current_user.subscription_tier
    ):
        raise HTTPException(status_code=429, detail="Rate limit exceeded")
    
    try:
        options_manager = OptionsStrategyManager()
        
        result = await options_manager.execute_options_strategy(
            strategy_id=strategy_id,
            is_paper_trade=is_paper_trade
        )
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error executing options strategy: {str(e)}")

@router.get("/options/strategies/{strategy_id}/pnl", tags=["Options Trading"])
async def get_options_strategy_pnl(
    strategy_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get P&L for an options strategy"""
    
    try:
        options_manager = OptionsStrategyManager()
        
        result = await options_manager.get_strategy_pnl(strategy_id)
        
        if "error" in result:
            raise HTTPException(status_code=404, detail=result["error"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting strategy P&L: {str(e)}")

# Algorithmic Trading Endpoints
@router.post("/algorithmic/signals", tags=["Algorithmic Trading"])
async def generate_algorithmic_signals(
    request: AlgorithmicStrategyRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Generate signals from algorithmic strategy"""
    
    # Rate limiting
    if not await algo_rate_limiter.check_rate_limit(
        f"algo_signals_{current_user.id}", 
        current_user.subscription_tier
    ):
        raise HTTPException(status_code=429, detail="Rate limit exceeded")
    
    try:
        algo_manager = AlgorithmicStrategyManager()
        
        # Get account balance (simplified - would normally query from broker)
        account_balance = 100000.0  # Default for demo
        
        signals = await algo_manager.run_strategy(
            strategy_id=0,  # Temporary ID for signal generation
            strategy_type=request.strategy_type,
            parameters=request.parameters,
            symbols=request.symbols,
            account_balance=account_balance
        )
        
        return {
            "success": True,
            "strategy_type": request.strategy_type,
            "signals_count": len(signals),
            "signals": signals,
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating algorithmic signals: {str(e)}")

@router.get("/algorithmic/strategies", tags=["Algorithmic Trading"])
async def get_available_strategies(
    current_user: User = Depends(get_current_user)
):
    """Get available algorithmic strategies"""
    
    try:
        algo_manager = AlgorithmicStrategyManager()
        strategies = algo_manager.get_available_strategies()
        
        return {
            "success": True,
            "strategies": strategies
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting strategies: {str(e)}")

@router.get("/algorithmic/performance/{strategy_id}", tags=["Algorithmic Trading"])
async def get_strategy_performance(
    strategy_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get performance metrics for an algorithmic strategy"""
    
    try:
        algo_manager = AlgorithmicStrategyManager()
        
        performance = await algo_manager.get_strategy_performance(strategy_id)
        
        return {
            "success": True,
            "strategy_id": strategy_id,
            "performance": performance
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting strategy performance: {str(e)}")

# Risk Management Endpoints
@router.post("/risk/assessment", tags=["Risk Management"])
async def comprehensive_risk_assessment(
    request: RiskAssessmentRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Perform comprehensive risk assessment"""
    
    try:
        risk_manager = AdvancedRiskManager()
        
        assessment = await risk_manager.comprehensive_risk_assessment(
            user_id=current_user.id,
            broker_account_id=request.broker_account_id
        )
        
        return {
            "success": True,
            "assessment": assessment
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error performing risk assessment: {str(e)}")

@router.get("/risk/greeks/{broker_account_id}", tags=["Risk Management"])
async def get_portfolio_greeks(
    broker_account_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get portfolio-level Greeks"""
    
    try:
        risk_manager = AdvancedRiskManager()
        
        greeks = await risk_manager.portfolio_greeks.calculate_portfolio_greeks(
            user_id=current_user.id,
            broker_account_id=broker_account_id
        )
        
        return {
            "success": True,
            "greeks": greeks
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting portfolio Greeks: {str(e)}")

@router.get("/risk/correlations/{broker_account_id}", tags=["Risk Management"])
async def get_portfolio_correlations(
    broker_account_id: int,
    lookback_days: int = Query(default=60, ge=30, le=252),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get portfolio correlation analysis"""
    
    try:
        risk_manager = AdvancedRiskManager()
        
        correlations = await risk_manager.correlation_analyzer.analyze_portfolio_correlations(
            user_id=current_user.id,
            broker_account_id=broker_account_id,
            lookback_days=lookback_days
        )
        
        return {
            "success": True,
            "correlations": correlations
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting portfolio correlations: {str(e)}")

@router.post("/risk/hedge-recommendations/{broker_account_id}", tags=["Risk Management"])
async def get_hedge_recommendations(
    broker_account_id: int,
    target_delta: float = Query(default=0.0),
    target_gamma: float = Query(default=0.0),
    target_vega: float = Query(default=0.0),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get dynamic hedging recommendations"""
    
    try:
        risk_manager = AdvancedRiskManager()
        
        recommendations = await risk_manager.dynamic_hedging.calculate_hedge_requirements(
            user_id=current_user.id,
            broker_account_id=broker_account_id,
            target_delta=target_delta,
            target_gamma=target_gamma,
            target_vega=target_vega
        )
        
        return {
            "success": True,
            "recommendations": recommendations
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting hedge recommendations: {str(e)}")

# Real-time Market Data WebSocket
@router.websocket("/ws/market-data/{user_id}")
async def websocket_market_data(websocket: WebSocket, user_id: int):
    """WebSocket endpoint for real-time market data"""
    from app.services.realtime_market_data import realtime_service

    # Start service if not running
    if not realtime_service.is_running:
        await realtime_service.start()

    await realtime_service.handle_websocket_connection(websocket, user_id)
