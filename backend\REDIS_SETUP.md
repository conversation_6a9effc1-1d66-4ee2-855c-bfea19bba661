# Redis Setup Guide for MarketHawk

## Overview

MarketHawk uses Redis for caching and rate limiting. While the application can run without Redis (using fallback methods), having Redis provides better performance and distributed rate limiting capabilities.

## Current Status

✅ **Application works without Redis** - Fallback methods are implemented
⚠️ **Redis not installed** - Running in development mode

## Installation Options

### Option 1: Docker (Recommended)

If you have Docker installed:

```bash
# Run Redis in Docker
docker run -d --name markethawk-redis -p 6379:6379 redis:7-alpine

# Or use the full docker-compose setup
docker-compose up redis
```

### Option 2: Windows Installation

1. **Download Redis for Windows:**
   - Visit: https://github.com/microsoftarchive/redis/releases
   - Download the latest .msi installer
   - Install Redis as a Windows service

2. **Alternative - WSL2:**
   ```bash
   # In WSL2 Ubuntu
   sudo apt update
   sudo apt install redis-server
   sudo service redis-server start
   ```

3. **Alternative - Chocolatey:**
   ```powershell
   # Install Chocolatey first, then:
   choco install redis-64
   ```

### Option 3: Cloud Redis (Production)

For production deployment, consider:
- **AWS ElastiCache**
- **Azure Cache for Redis**
- **Google Cloud Memorystore**
- **Redis Cloud**

## Configuration

Update your `.env` file:

```env
# Local Redis
REDIS_URL=redis://localhost:6379/0

# Redis with password
REDIS_URL=redis://:password@localhost:6379/0

# Cloud Redis
REDIS_URL=redis://username:password@your-redis-host:6379/0
```

## Testing Redis Connection

After installation, test the connection:

```bash
# Test Redis CLI
redis-cli ping
# Should return: PONG

# Test from Python
python -c "import redis; r=redis.from_url('redis://localhost:6379/0'); print(r.ping())"
```

## Restart Application

After Redis is installed and running:

```bash
# Restart the backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

You should see:
```
✅ Redis connection established successfully
```

## Features Enabled with Redis

- **Distributed Rate Limiting**: Proper rate limiting across multiple instances
- **Session Caching**: Faster user session management  
- **Market Data Caching**: Cached API responses for better performance
- **Real-time Features**: WebSocket connection management

## Troubleshooting

### Connection Refused
- Ensure Redis server is running: `redis-cli ping`
- Check port 6379 is not blocked by firewall
- Verify REDIS_URL in .env file

### Permission Denied
- On Linux/WSL: `sudo service redis-server start`
- On Windows: Start Redis service in Services.msc

### Memory Issues
- Configure Redis memory limit in redis.conf
- Set appropriate eviction policies

## Development vs Production

**Development (Current):**
- Redis optional
- Fallback to in-memory caching
- Single instance rate limiting

**Production (Recommended):**
- Redis required
- Distributed caching
- Proper session management
- Multi-instance rate limiting
