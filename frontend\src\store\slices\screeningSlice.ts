import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Strategy {
  id: number;
  name: string;
  description: string;
  screening_criteria: any[];
  universe: string[];
  min_score: number;
  max_results: number;
  is_active: boolean;
  schedule_enabled: boolean;
  schedule_frequency: string;
  created_at: string;
  updated_at: string;
  last_run: string;
  total_runs: number;
}

interface ScreeningState {
  strategies: Strategy[];
  selectedStrategy: Strategy | null;
  screeningResults: any[];
  isLoading: boolean;
  error: string | null;
}

const initialState: ScreeningState = {
  strategies: [],
  selectedStrategy: null,
  screeningResults: [],
  isLoading: false,
  error: null,
};

const screeningSlice = createSlice({
  name: 'screening',
  initialState,
  reducers: {
    setStrategies: (state, action: PayloadAction<Strategy[]>) => {
      state.strategies = action.payload;
    },
    setSelectedStrategy: (state, action: PayloadAction<Strategy | null>) => {
      state.selectedStrategy = action.payload;
    },
    setScreeningResults: (state, action: PayloadAction<any[]>) => {
      state.screeningResults = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setStrategies,
  setSelectedStrategy,
  setScreeningResults,
  setLoading,
  setError,
} = screeningSlice.actions;
export default screeningSlice.reducer;
