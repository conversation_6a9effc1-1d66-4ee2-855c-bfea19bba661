"""
Database configuration and connection management
"""

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import MetaData
from influxdb_client.client.influxdb_client_async import InfluxDBClientAsync
from influxdb_client.client.write_api_async import WriteApiAsync
from influxdb_client.client.query_api_async import QueryApiAsync
import redis.asyncio as redis
from contextlib import asynccontextmanager
import logging
from typing import AsyncGenerator, Optional

from app.core.config import settings, derived_settings

logger = logging.getLogger(__name__)

# SQLAlchemy Base
class Base(DeclarativeBase):
    """Base class for SQLAlchemy models"""
    metadata = MetaData(
        naming_convention={
            "ix": "ix_%(column_0_label)s",
            "uq": "uq_%(table_name)s_%(column_0_name)s",
            "ck": "ck_%(table_name)s_%(constraint_name)s",
            "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
            "pk": "pk_%(table_name)s"
        }
    )

# Database engines and sessions
postgres_engine = None
postgres_session_factory = None
influxdb_client = None
redis_client = None

async def init_db():
    """Initialize database connections"""
    global postgres_engine, postgres_session_factory, influxdb_client, redis_client
    
    try:
        # Database connection
        db_config = derived_settings.get_database_config()

        # Create engine with appropriate configuration
        engine_kwargs = {
            "echo": db_config["echo"]
        }

        # Add PostgreSQL-specific settings if available
        for key in ["pool_size", "max_overflow", "pool_pre_ping", "pool_recycle"]:
            if key in db_config:
                engine_kwargs[key] = db_config[key]

        postgres_engine = create_async_engine(
            db_config["url"],
            **engine_kwargs
        )
        
        postgres_session_factory = async_sessionmaker(
            postgres_engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # Create tables
        async with postgres_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("PostgreSQL database initialized successfully")
        
        # InfluxDB connection (optional for development)
        try:
            influx_config = derived_settings.get_influxdb_config()
            influxdb_client = InfluxDBClientAsync(
                url=influx_config["url"],
                token=influx_config["token"],
                org=influx_config["org"]
            )
            logger.info("InfluxDB client initialized (connection will be tested on first use)")
        except Exception as e:
            logger.warning(f"InfluxDB initialization failed: {e}")
            influxdb_client = None

        # Redis connection (optional for development)
        try:
            redis_config = derived_settings.get_redis_config()
            redis_client = redis.from_url(
                redis_config["url"],
                decode_responses=redis_config["decode_responses"],
                socket_keepalive=redis_config["socket_keepalive"],
                health_check_interval=redis_config["health_check_interval"]
            )

            # Test Redis connection
            await redis_client.ping()
            logger.info("✅ Redis connection established successfully")
        except Exception as e:
            logger.info(f"⚠️  Redis connection failed: {e}")
            logger.info("📝 Running in development mode without Redis - caching and rate limiting will use fallback methods")
            redis_client = None
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise

async def close_db():
    """Close database connections"""
    global postgres_engine, influxdb_client, redis_client
    
    try:
        if postgres_engine:
            await postgres_engine.dispose()
            logger.info("PostgreSQL connection closed")
        
        if influxdb_client:
            await influxdb_client.close()
            logger.info("InfluxDB connection closed")
        
        if redis_client:
            await redis_client.close()
            logger.info("Redis connection closed")
            
    except Exception as e:
        logger.error(f"Error closing database connections: {e}")

@asynccontextmanager
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Get PostgreSQL database session"""
    if not postgres_session_factory:
        raise RuntimeError("Database not initialized")
    
    async with postgres_session_factory() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()

async def get_influxdb() -> InfluxDBClientAsync:
    """Get InfluxDB client"""
    if not influxdb_client:
        raise RuntimeError("InfluxDB not initialized")
    return influxdb_client

async def get_influxdb_write_api() -> WriteApiAsync:
    """Get InfluxDB write API"""
    client = await get_influxdb()
    return client.write_api()

async def get_influxdb_query_api() -> QueryApiAsync:
    """Get InfluxDB query API"""
    client = await get_influxdb()
    return client.query_api()

async def get_redis() -> redis.Redis:
    """Get Redis client"""
    if not redis_client:
        raise RuntimeError("Redis not available - running in development mode without Redis. Install Redis or use Docker for full functionality.")
    return redis_client

# Database utilities
class DatabaseManager:
    """Database management utilities"""
    
    @staticmethod
    async def health_check() -> dict:
        """Check health of all database connections"""
        health_status = {
            "postgres": "unknown",
            "influxdb": "unknown",
            "redis": "unknown"
        }
        
        # Check PostgreSQL
        try:
            async with get_db() as db:
                await db.execute("SELECT 1")
                health_status["postgres"] = "healthy"
        except Exception as e:
            health_status["postgres"] = f"unhealthy: {str(e)}"
        
        # Check InfluxDB
        try:
            client = await get_influxdb()
            health = await client.health()
            health_status["influxdb"] = "healthy" if health.status == "pass" else f"unhealthy: {health.message}"
        except Exception as e:
            health_status["influxdb"] = f"unhealthy: {str(e)}"
        
        # Check Redis
        try:
            redis_conn = await get_redis()
            await redis_conn.ping()
            health_status["redis"] = "healthy"
        except Exception as e:
            if "not available" in str(e):
                health_status["redis"] = "not available (development mode)"
            else:
                health_status["redis"] = f"unhealthy: {str(e)}"
        
        return health_status
    
    @staticmethod
    async def get_connection_info() -> dict:
        """Get database connection information"""
        return {
            "postgres": {
                "url": settings.DATABASE_URL.split("@")[1] if "@" in settings.DATABASE_URL else "hidden",
                "pool_size": 20,
                "max_overflow": 30
            },
            "influxdb": {
                "url": settings.INFLUXDB_URL,
                "org": settings.INFLUXDB_ORG,
                "bucket": settings.INFLUXDB_BUCKET
            },
            "redis": {
                "url": settings.REDIS_URL.split("@")[1] if "@" in settings.REDIS_URL else settings.REDIS_URL
            }
        }

# Time-series data utilities
class TimeSeriesManager:
    """InfluxDB time-series data management"""
    
    @staticmethod
    async def write_market_data(symbol: str, data: dict, timestamp: Optional[str] = None):
        """Write market data to InfluxDB"""
        try:
            write_api = await get_influxdb_write_api()
            
            point = {
                "measurement": "market_data",
                "tags": {"symbol": symbol},
                "fields": data,
                "time": timestamp
            }
            
            await write_api.write(
                bucket=settings.INFLUXDB_BUCKET,
                org=settings.INFLUXDB_ORG,
                record=point
            )
            
        except Exception as e:
            logger.error(f"Error writing market data for {symbol}: {e}")
            raise
    
    @staticmethod
    async def query_market_data(symbol: str, start_time: str, end_time: str) -> list:
        """Query market data from InfluxDB"""
        try:
            query_api = await get_influxdb_query_api()
            
            query = f'''
                from(bucket: "{settings.INFLUXDB_BUCKET}")
                |> range(start: {start_time}, stop: {end_time})
                |> filter(fn: (r) => r._measurement == "market_data")
                |> filter(fn: (r) => r.symbol == "{symbol}")
            '''
            
            result = await query_api.query(query, org=settings.INFLUXDB_ORG)
            return result
            
        except Exception as e:
            logger.error(f"Error querying market data for {symbol}: {e}")
            raise

# Cache utilities
class CacheManager:
    """Redis cache management"""
    
    @staticmethod
    async def set(key: str, value: str, expire: int = 3600):
        """Set cache value with expiration"""
        try:
            redis_conn = await get_redis()
            await redis_conn.setex(key, expire, value)
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {e}")
    
    @staticmethod
    async def get(key: str) -> Optional[str]:
        """Get cache value"""
        try:
            redis_conn = await get_redis()
            return await redis_conn.get(key)
        except Exception as e:
            logger.error(f"Error getting cache key {key}: {e}")
            return None
    
    @staticmethod
    async def delete(key: str):
        """Delete cache key"""
        try:
            redis_conn = await get_redis()
            await redis_conn.delete(key)
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {e}")
    
    @staticmethod
    async def exists(key: str) -> bool:
        """Check if cache key exists"""
        try:
            redis_conn = await get_redis()
            return await redis_conn.exists(key)
        except Exception as e:
            logger.error(f"Error checking cache key {key}: {e}")
            return False
