"""
Strategy optimization service using genetic algorithms and walk-forward analysis
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import random
from concurrent.futures import ThreadPoolExecutor

from app.services.algorithmic_strategies import AlgorithmicStrategyManager
from app.services.backtesting_engine import BacktestingEngine

logger = logging.getLogger(__name__)

@dataclass
class OptimizationResult:
    """Optimization result container"""
    parameters: Dict[str, Any]
    fitness_score: float
    sharpe_ratio: float
    total_return: float
    max_drawdown: float
    win_rate: float
    total_trades: int

class GeneticOptimizer:
    """Genetic algorithm for strategy parameter optimization"""
    
    def __init__(self, population_size: int = 50, generations: int = 20, mutation_rate: float = 0.1):
        self.population_size = population_size
        self.generations = generations
        self.mutation_rate = mutation_rate
        self.strategy_manager = AlgorithmicStrategyManager()
        self.backtesting_engine = BacktestingEngine()
        
    async def optimize_strategy(
        self,
        strategy_type: str,
        parameter_ranges: Dict[str, Dict[str, Any]],
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        initial_capital: float = 100000
    ) -> List[OptimizationResult]:
        """Optimize strategy parameters using genetic algorithm"""
        
        try:
            logger.info(f"Starting genetic optimization for {strategy_type}")
            
            # Initialize population
            population = self._initialize_population(parameter_ranges)
            
            best_results = []
            
            for generation in range(self.generations):
                logger.info(f"Processing generation {generation + 1}/{self.generations}")
                
                # Evaluate fitness for each individual
                fitness_scores = await self._evaluate_population(
                    population, strategy_type, symbols, start_date, end_date, initial_capital
                )
                
                # Create results for this generation
                generation_results = [
                    OptimizationResult(
                        parameters=individual,
                        fitness_score=fitness_scores[i]["fitness"],
                        sharpe_ratio=fitness_scores[i]["sharpe_ratio"],
                        total_return=fitness_scores[i]["total_return"],
                        max_drawdown=fitness_scores[i]["max_drawdown"],
                        win_rate=fitness_scores[i]["win_rate"],
                        total_trades=fitness_scores[i]["total_trades"]
                    )
                    for i, individual in enumerate(population)
                ]
                
                # Sort by fitness
                generation_results.sort(key=lambda x: x.fitness_score, reverse=True)
                
                # Keep track of best results
                if not best_results or generation_results[0].fitness_score > best_results[0].fitness_score:
                    best_results = generation_results[:10]  # Keep top 10
                
                # Selection and reproduction
                if generation < self.generations - 1:
                    population = self._evolve_population(population, fitness_scores)
                
                logger.info(f"Generation {generation + 1} best fitness: {generation_results[0].fitness_score:.4f}")
            
            return best_results
            
        except Exception as e:
            logger.error(f"Error in genetic optimization: {e}")
            return []
    
    def _initialize_population(self, parameter_ranges: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Initialize random population"""
        population = []
        
        for _ in range(self.population_size):
            individual = {}
            
            for param_name, param_config in parameter_ranges.items():
                param_type = param_config["type"]
                min_val = param_config["min"]
                max_val = param_config["max"]
                
                if param_type == "int":
                    individual[param_name] = random.randint(min_val, max_val)
                elif param_type == "float":
                    individual[param_name] = random.uniform(min_val, max_val)
                elif param_type == "choice":
                    individual[param_name] = random.choice(param_config["choices"])
            
            population.append(individual)
        
        return population
    
    async def _evaluate_population(
        self,
        population: List[Dict[str, Any]],
        strategy_type: str,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        initial_capital: float
    ) -> List[Dict[str, Any]]:
        """Evaluate fitness for entire population"""
        
        fitness_scores = []
        
        # Use ThreadPoolExecutor for parallel evaluation
        with ThreadPoolExecutor(max_workers=4) as executor:
            tasks = []
            
            for individual in population:
                task = asyncio.create_task(
                    self._evaluate_individual(
                        individual, strategy_type, symbols, start_date, end_date, initial_capital
                    )
                )
                tasks.append(task)
            
            # Wait for all evaluations to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"Error evaluating individual: {result}")
                    fitness_scores.append({
                        "fitness": 0.0,
                        "sharpe_ratio": 0.0,
                        "total_return": 0.0,
                        "max_drawdown": 0.0,
                        "win_rate": 0.0,
                        "total_trades": 0
                    })
                else:
                    fitness_scores.append(result)
        
        return fitness_scores
    
    async def _evaluate_individual(
        self,
        parameters: Dict[str, Any],
        strategy_type: str,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        initial_capital: float
    ) -> Dict[str, Any]:
        """Evaluate fitness of individual parameter set"""
        
        try:
            # Create strategy with these parameters
            strategy = self.strategy_manager.create_strategy(strategy_type, parameters)
            
            if not strategy:
                return {"fitness": 0.0, "sharpe_ratio": 0.0, "total_return": 0.0, 
                       "max_drawdown": 0.0, "win_rate": 0.0, "total_trades": 0}
            
            # Run backtest
            backtest_results = await self.backtesting_engine.run_backtest(
                strategy=strategy,
                start_date=start_date,
                end_date=end_date,
                initial_capital=initial_capital,
                symbols=symbols
            )
            
            if not backtest_results or "error" in backtest_results:
                return {"fitness": 0.0, "sharpe_ratio": 0.0, "total_return": 0.0, 
                       "max_drawdown": 0.0, "win_rate": 0.0, "total_trades": 0}
            
            # Extract metrics
            metrics = backtest_results.get("performance_metrics", {})
            
            sharpe_ratio = metrics.get("sharpe_ratio", 0.0)
            total_return = metrics.get("total_return_percent", 0.0)
            max_drawdown = abs(metrics.get("max_drawdown_percent", 0.0))
            win_rate = metrics.get("win_rate", 0.0)
            total_trades = metrics.get("total_trades", 0)
            
            # Calculate fitness score (weighted combination of metrics)
            fitness = self._calculate_fitness(sharpe_ratio, total_return, max_drawdown, win_rate, total_trades)
            
            return {
                "fitness": fitness,
                "sharpe_ratio": sharpe_ratio,
                "total_return": total_return,
                "max_drawdown": max_drawdown,
                "win_rate": win_rate,
                "total_trades": total_trades
            }
            
        except Exception as e:
            logger.error(f"Error evaluating individual: {e}")
            return {"fitness": 0.0, "sharpe_ratio": 0.0, "total_return": 0.0, 
                   "max_drawdown": 0.0, "win_rate": 0.0, "total_trades": 0}
    
    def _calculate_fitness(
        self,
        sharpe_ratio: float,
        total_return: float,
        max_drawdown: float,
        win_rate: float,
        total_trades: int
    ) -> float:
        """Calculate fitness score from performance metrics"""
        
        # Normalize metrics
        sharpe_score = max(0, min(sharpe_ratio / 3.0, 1.0))  # Cap at 3.0
        return_score = max(0, min(total_return / 50.0, 1.0))  # Cap at 50%
        drawdown_score = max(0, 1.0 - (max_drawdown / 30.0))  # Penalize >30% drawdown
        win_rate_score = win_rate / 100.0
        trade_count_score = min(total_trades / 100.0, 1.0)  # Prefer more trades up to 100
        
        # Weighted fitness calculation
        fitness = (
            sharpe_score * 0.4 +      # 40% weight on risk-adjusted returns
            return_score * 0.25 +     # 25% weight on total returns
            drawdown_score * 0.2 +    # 20% weight on drawdown control
            win_rate_score * 0.1 +    # 10% weight on win rate
            trade_count_score * 0.05  # 5% weight on trade frequency
        )
        
        return fitness
    
    def _evolve_population(
        self,
        population: List[Dict[str, Any]],
        fitness_scores: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Evolve population through selection, crossover, and mutation"""
        
        # Sort population by fitness
        sorted_indices = sorted(range(len(fitness_scores)), 
                              key=lambda i: fitness_scores[i]["fitness"], reverse=True)
        
        new_population = []
        
        # Elitism: Keep top 20% of population
        elite_count = int(0.2 * self.population_size)
        for i in range(elite_count):
            new_population.append(population[sorted_indices[i]].copy())
        
        # Generate rest through crossover and mutation
        while len(new_population) < self.population_size:
            # Tournament selection
            parent1 = self._tournament_selection(population, fitness_scores)
            parent2 = self._tournament_selection(population, fitness_scores)
            
            # Crossover
            child = self._crossover(parent1, parent2)
            
            # Mutation
            child = self._mutate(child)
            
            new_population.append(child)
        
        return new_population
    
    def _tournament_selection(
        self,
        population: List[Dict[str, Any]],
        fitness_scores: List[Dict[str, Any]],
        tournament_size: int = 3
    ) -> Dict[str, Any]:
        """Tournament selection for parent selection"""
        
        tournament_indices = random.sample(range(len(population)), tournament_size)
        best_index = max(tournament_indices, key=lambda i: fitness_scores[i]["fitness"])
        
        return population[best_index].copy()
    
    def _crossover(self, parent1: Dict[str, Any], parent2: Dict[str, Any]) -> Dict[str, Any]:
        """Single-point crossover"""
        
        child = {}
        keys = list(parent1.keys())
        crossover_point = random.randint(0, len(keys) - 1)
        
        for i, key in enumerate(keys):
            if i <= crossover_point:
                child[key] = parent1[key]
            else:
                child[key] = parent2[key]
        
        return child
    
    def _mutate(self, individual: Dict[str, Any]) -> Dict[str, Any]:
        """Mutate individual parameters"""
        
        mutated = individual.copy()
        
        for key, value in mutated.items():
            if random.random() < self.mutation_rate:
                if isinstance(value, int):
                    # Add random noise to integer parameters
                    noise = random.randint(-2, 2)
                    mutated[key] = max(1, value + noise)
                elif isinstance(value, float):
                    # Add random noise to float parameters
                    noise = random.uniform(-0.1, 0.1) * value
                    mutated[key] = max(0.001, value + noise)
        
        return mutated

class WalkForwardAnalysis:
    """Walk-forward analysis for strategy validation"""
    
    def __init__(self):
        self.strategy_manager = AlgorithmicStrategyManager()
        self.backtesting_engine = BacktestingEngine()
    
    async def run_walk_forward_analysis(
        self,
        strategy_type: str,
        parameters: Dict[str, Any],
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        optimization_window: int = 252,  # 1 year
        test_window: int = 63,  # 3 months
        initial_capital: float = 100000
    ) -> Dict[str, Any]:
        """Run walk-forward analysis"""
        
        try:
            results = []
            current_date = start_date
            
            while current_date + timedelta(days=optimization_window + test_window) <= end_date:
                # Define optimization and test periods
                opt_start = current_date
                opt_end = current_date + timedelta(days=optimization_window)
                test_start = opt_end
                test_end = test_start + timedelta(days=test_window)
                
                logger.info(f"Walk-forward period: {opt_start.date()} to {test_end.date()}")
                
                # Optimize parameters on optimization period
                optimizer = GeneticOptimizer(population_size=20, generations=10)
                
                # Define parameter ranges (simplified for demo)
                parameter_ranges = {
                    "lookback_period": {"type": "int", "min": 10, "max": 50},
                    "momentum_threshold": {"type": "float", "min": 0.01, "max": 0.10},
                    "max_position_size": {"type": "float", "min": 0.01, "max": 0.10}
                }
                
                optimization_results = await optimizer.optimize_strategy(
                    strategy_type, parameter_ranges, symbols, opt_start, opt_end, initial_capital
                )
                
                if not optimization_results:
                    current_date = test_end
                    continue
                
                # Use best parameters for out-of-sample test
                best_params = optimization_results[0].parameters
                
                # Test on out-of-sample period
                strategy = self.strategy_manager.create_strategy(strategy_type, best_params)
                
                test_results = await self.backtesting_engine.run_backtest(
                    strategy=strategy,
                    start_date=test_start,
                    end_date=test_end,
                    initial_capital=initial_capital,
                    symbols=symbols
                )
                
                if test_results and "error" not in test_results:
                    metrics = test_results.get("performance_metrics", {})
                    
                    results.append({
                        "optimization_period": {"start": opt_start.isoformat(), "end": opt_end.isoformat()},
                        "test_period": {"start": test_start.isoformat(), "end": test_end.isoformat()},
                        "optimized_parameters": best_params,
                        "test_performance": {
                            "total_return": metrics.get("total_return_percent", 0.0),
                            "sharpe_ratio": metrics.get("sharpe_ratio", 0.0),
                            "max_drawdown": metrics.get("max_drawdown_percent", 0.0),
                            "win_rate": metrics.get("win_rate", 0.0),
                            "total_trades": metrics.get("total_trades", 0)
                        }
                    })
                
                # Move to next period
                current_date = test_end
            
            # Calculate aggregate statistics
            if results:
                aggregate_stats = self._calculate_aggregate_stats(results)
                
                return {
                    "walk_forward_results": results,
                    "aggregate_statistics": aggregate_stats,
                    "total_periods": len(results)
                }
            else:
                return {"error": "No valid walk-forward results"}
                
        except Exception as e:
            logger.error(f"Error in walk-forward analysis: {e}")
            return {"error": str(e)}
    
    def _calculate_aggregate_stats(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate aggregate statistics from walk-forward results"""
        
        returns = [r["test_performance"]["total_return"] for r in results]
        sharpe_ratios = [r["test_performance"]["sharpe_ratio"] for r in results]
        drawdowns = [r["test_performance"]["max_drawdown"] for r in results]
        win_rates = [r["test_performance"]["win_rate"] for r in results]
        
        return {
            "average_return": np.mean(returns),
            "return_std": np.std(returns),
            "average_sharpe": np.mean(sharpe_ratios),
            "average_drawdown": np.mean(drawdowns),
            "average_win_rate": np.mean(win_rates),
            "consistency_score": len([r for r in returns if r > 0]) / len(returns) * 100,
            "best_period_return": max(returns),
            "worst_period_return": min(returns)
        }

# Global instances
genetic_optimizer = GeneticOptimizer()
walk_forward_analyzer = WalkForwardAnalysis()
