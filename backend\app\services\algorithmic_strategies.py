"""
Algorithmic trading strategies with pre-built algorithms and customizable parameters
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from sqlalchemy import select, and_, or_
from abc import ABC, abstractmethod

from app.core.config import settings
from app.core.database import get_db
from app.models.stock import Stock, MarketData
from app.models.trade import Trade, OrderType, OrderSide, OrderStatus
from app.models.portfolio import Strategy
from app.models.user import BrokerAccount
from app.services.data_provider import DataProvider
from app.utils.indicators import TechnicalIndicators

logger = logging.getLogger(__name__)

class AlgorithmicStrategy(ABC):
    """Base class for algorithmic trading strategies"""
    
    def __init__(self, name: str, parameters: Dict[str, Any]):
        self.name = name
        self.parameters = parameters
        self.data_provider = DataProvider()
        self.technical_indicators = TechnicalIndicators()
        self.is_running = False
        
    @abstractmethod
    async def generate_signals(self, symbols: List[str], timeframe: str = "1d") -> List[Dict[str, Any]]:
        """Generate trading signals for given symbols"""
        pass
    
    @abstractmethod
    async def calculate_position_size(self, signal: Dict[str, Any], account_balance: float) -> int:
        """Calculate position size for a signal"""
        pass
    
    @abstractmethod
    def validate_parameters(self) -> bool:
        """Validate strategy parameters"""
        pass

class MomentumStrategy(AlgorithmicStrategy):
    """Momentum-based algorithmic strategy"""
    
    def __init__(self, parameters: Dict[str, Any]):
        super().__init__("Momentum Strategy", parameters)
        
        # Default parameters
        self.lookback_period = parameters.get("lookback_period", 20)
        self.momentum_threshold = parameters.get("momentum_threshold", 0.05)  # 5%
        self.rsi_oversold = parameters.get("rsi_oversold", 30)
        self.rsi_overbought = parameters.get("rsi_overbought", 70)
        self.volume_multiplier = parameters.get("volume_multiplier", 1.5)
        self.max_position_size = parameters.get("max_position_size", 0.05)  # 5% of portfolio
        
    def validate_parameters(self) -> bool:
        """Validate momentum strategy parameters"""
        required_params = ["lookback_period", "momentum_threshold"]
        return all(param in self.parameters for param in required_params)
    
    async def generate_signals(self, symbols: List[str], timeframe: str = "1d") -> List[Dict[str, Any]]:
        """Generate momentum-based trading signals"""
        signals = []
        
        try:
            for symbol in symbols:
                # Get historical data
                historical_data = await self.data_provider.get_historical_data(
                    symbol, period=f"{self.lookback_period * 2}d", interval=timeframe
                )
                
                if historical_data is None or len(historical_data) < self.lookback_period:
                    continue
                
                # Calculate technical indicators
                indicators = await self.technical_indicators.calculate_all_indicators(historical_data)
                
                # Get current values
                current_price = historical_data['close'].iloc[-1]
                previous_price = historical_data['close'].iloc[-self.lookback_period]
                current_volume = historical_data['volume'].iloc[-1]
                avg_volume = historical_data['volume'].rolling(20).mean().iloc[-1]
                
                # Calculate momentum
                momentum = (current_price - previous_price) / previous_price
                
                # Get RSI
                rsi = indicators.get('rsi', {}).get('value', 50)
                
                # Generate buy signal
                if (momentum > self.momentum_threshold and 
                    rsi < self.rsi_overbought and 
                    current_volume > avg_volume * self.volume_multiplier):
                    
                    signals.append({
                        "symbol": symbol,
                        "action": "buy",
                        "signal_strength": min(momentum * 100, 100),
                        "entry_price": current_price,
                        "stop_loss": current_price * 0.95,  # 5% stop loss
                        "take_profit": current_price * 1.15,  # 15% take profit
                        "momentum": momentum,
                        "rsi": rsi,
                        "volume_ratio": current_volume / avg_volume,
                        "strategy": "momentum",
                        "timestamp": datetime.utcnow()
                    })
                
                # Generate sell signal for existing positions
                elif (momentum < -self.momentum_threshold or 
                      rsi > self.rsi_overbought):
                    
                    signals.append({
                        "symbol": symbol,
                        "action": "sell",
                        "signal_strength": min(abs(momentum) * 100, 100),
                        "exit_price": current_price,
                        "momentum": momentum,
                        "rsi": rsi,
                        "strategy": "momentum",
                        "timestamp": datetime.utcnow()
                    })
                    
        except Exception as e:
            logger.error(f"Error generating momentum signals: {e}")
            
        return signals
    
    async def calculate_position_size(self, signal: Dict[str, Any], account_balance: float) -> int:
        """Calculate position size based on momentum strength and risk"""
        try:
            signal_strength = signal.get("signal_strength", 50) / 100
            risk_per_trade = self.max_position_size * signal_strength
            
            entry_price = signal.get("entry_price", 0)
            stop_loss = signal.get("stop_loss", entry_price * 0.95)
            
            if entry_price <= 0 or stop_loss <= 0:
                return 0
            
            # Calculate risk per share
            risk_per_share = abs(entry_price - stop_loss)
            
            # Calculate position size
            max_risk_amount = account_balance * risk_per_trade
            position_size = int(max_risk_amount / risk_per_share)
            
            return max(1, position_size)
            
        except Exception as e:
            logger.error(f"Error calculating momentum position size: {e}")
            return 0

class MeanReversionStrategy(AlgorithmicStrategy):
    """Mean reversion algorithmic strategy"""
    
    def __init__(self, parameters: Dict[str, Any]):
        super().__init__("Mean Reversion Strategy", parameters)
        
        # Default parameters
        self.lookback_period = parameters.get("lookback_period", 20)
        self.std_dev_threshold = parameters.get("std_dev_threshold", 2.0)
        self.rsi_oversold = parameters.get("rsi_oversold", 30)
        self.rsi_overbought = parameters.get("rsi_overbought", 70)
        self.bollinger_period = parameters.get("bollinger_period", 20)
        self.max_position_size = parameters.get("max_position_size", 0.03)  # 3% of portfolio
        
    def validate_parameters(self) -> bool:
        """Validate mean reversion strategy parameters"""
        required_params = ["lookback_period", "std_dev_threshold"]
        return all(param in self.parameters for param in required_params)
    
    async def generate_signals(self, symbols: List[str], timeframe: str = "1d") -> List[Dict[str, Any]]:
        """Generate mean reversion trading signals"""
        signals = []
        
        try:
            for symbol in symbols:
                # Get historical data
                historical_data = await self.data_provider.get_historical_data(
                    symbol, period=f"{self.lookback_period * 2}d", interval=timeframe
                )
                
                if historical_data is None or len(historical_data) < self.lookback_period:
                    continue
                
                # Calculate technical indicators
                indicators = await self.technical_indicators.calculate_all_indicators(historical_data)
                
                # Get current values
                current_price = historical_data['close'].iloc[-1]
                
                # Calculate Bollinger Bands
                sma = historical_data['close'].rolling(self.bollinger_period).mean().iloc[-1]
                std = historical_data['close'].rolling(self.bollinger_period).std().iloc[-1]
                upper_band = sma + (std * self.std_dev_threshold)
                lower_band = sma - (std * self.std_dev_threshold)
                
                # Get RSI
                rsi = indicators.get('rsi', {}).get('value', 50)
                
                # Calculate distance from mean
                distance_from_mean = (current_price - sma) / sma
                
                # Generate buy signal (oversold condition)
                if (current_price <= lower_band and 
                    rsi <= self.rsi_oversold and 
                    distance_from_mean < -0.02):  # At least 2% below mean
                    
                    signals.append({
                        "symbol": symbol,
                        "action": "buy",
                        "signal_strength": min(abs(distance_from_mean) * 500, 100),
                        "entry_price": current_price,
                        "stop_loss": current_price * 0.95,
                        "take_profit": sma,  # Target mean reversion
                        "distance_from_mean": distance_from_mean,
                        "rsi": rsi,
                        "bollinger_position": (current_price - lower_band) / (upper_band - lower_band),
                        "strategy": "mean_reversion",
                        "timestamp": datetime.utcnow()
                    })
                
                # Generate sell signal (overbought condition)
                elif (current_price >= upper_band and 
                      rsi >= self.rsi_overbought and 
                      distance_from_mean > 0.02):  # At least 2% above mean
                    
                    signals.append({
                        "symbol": symbol,
                        "action": "sell",
                        "signal_strength": min(distance_from_mean * 500, 100),
                        "exit_price": current_price,
                        "distance_from_mean": distance_from_mean,
                        "rsi": rsi,
                        "bollinger_position": (current_price - lower_band) / (upper_band - lower_band),
                        "strategy": "mean_reversion",
                        "timestamp": datetime.utcnow()
                    })
                    
        except Exception as e:
            logger.error(f"Error generating mean reversion signals: {e}")
            
        return signals
    
    async def calculate_position_size(self, signal: Dict[str, Any], account_balance: float) -> int:
        """Calculate position size based on distance from mean"""
        try:
            signal_strength = signal.get("signal_strength", 50) / 100
            risk_per_trade = self.max_position_size * signal_strength
            
            entry_price = signal.get("entry_price", 0)
            stop_loss = signal.get("stop_loss", entry_price * 0.95)
            
            if entry_price <= 0 or stop_loss <= 0:
                return 0
            
            # Calculate risk per share
            risk_per_share = abs(entry_price - stop_loss)
            
            # Calculate position size
            max_risk_amount = account_balance * risk_per_trade
            position_size = int(max_risk_amount / risk_per_share)
            
            return max(1, position_size)
            
        except Exception as e:
            logger.error(f"Error calculating mean reversion position size: {e}")
            return 0


class PairsTradingStrategy(AlgorithmicStrategy):
    """Pairs trading algorithmic strategy"""

    def __init__(self, parameters: Dict[str, Any]):
        super().__init__("Pairs Trading Strategy", parameters)

        # Default parameters
        self.lookback_period = parameters.get("lookback_period", 60)
        self.correlation_threshold = parameters.get("correlation_threshold", 0.8)
        self.zscore_entry = parameters.get("zscore_entry", 2.0)
        self.zscore_exit = parameters.get("zscore_exit", 0.5)
        self.max_position_size = parameters.get("max_position_size", 0.02)  # 2% per leg
        self.pairs = parameters.get("pairs", [])  # List of symbol pairs

    def validate_parameters(self) -> bool:
        """Validate pairs trading strategy parameters"""
        required_params = ["lookback_period", "correlation_threshold", "pairs"]
        return all(param in self.parameters for param in required_params)

    async def generate_signals(self, symbols: List[str], timeframe: str = "1d") -> List[Dict[str, Any]]:
        """Generate pairs trading signals"""
        signals = []

        try:
            for pair in self.pairs:
                if len(pair) != 2:
                    continue

                symbol1, symbol2 = pair

                # Get historical data for both symbols
                data1 = await self.data_provider.get_historical_data(
                    symbol1, period=f"{self.lookback_period * 2}d", interval=timeframe
                )
                data2 = await self.data_provider.get_historical_data(
                    symbol2, period=f"{self.lookback_period * 2}d", interval=timeframe
                )

                if data1 is None or data2 is None or len(data1) < self.lookback_period:
                    continue

                # Align data by date
                aligned_data = pd.merge(
                    data1[['close']].rename(columns={'close': f'{symbol1}_close'}),
                    data2[['close']].rename(columns={'close': f'{symbol2}_close'}),
                    left_index=True, right_index=True, how='inner'
                )

                if len(aligned_data) < self.lookback_period:
                    continue

                # Calculate correlation
                correlation = aligned_data[f'{symbol1}_close'].corr(aligned_data[f'{symbol2}_close'])

                if abs(correlation) < self.correlation_threshold:
                    continue

                # Calculate spread
                spread = aligned_data[f'{symbol1}_close'] - aligned_data[f'{symbol2}_close']
                spread_mean = spread.rolling(self.lookback_period).mean().iloc[-1]
                spread_std = spread.rolling(self.lookback_period).std().iloc[-1]

                current_spread = spread.iloc[-1]
                zscore = (current_spread - spread_mean) / spread_std if spread_std > 0 else 0

                current_price1 = aligned_data[f'{symbol1}_close'].iloc[-1]
                current_price2 = aligned_data[f'{symbol2}_close'].iloc[-1]

                # Generate entry signals
                if abs(zscore) >= self.zscore_entry:
                    if zscore > 0:  # Symbol1 overpriced relative to Symbol2
                        # Sell Symbol1, Buy Symbol2
                        signals.extend([
                            {
                                "symbol": symbol1,
                                "action": "sell",
                                "signal_strength": min(abs(zscore) * 20, 100),
                                "entry_price": current_price1,
                                "pair_symbol": symbol2,
                                "zscore": zscore,
                                "correlation": correlation,
                                "strategy": "pairs_trading",
                                "pair_leg": "short",
                                "timestamp": datetime.utcnow()
                            },
                            {
                                "symbol": symbol2,
                                "action": "buy",
                                "signal_strength": min(abs(zscore) * 20, 100),
                                "entry_price": current_price2,
                                "pair_symbol": symbol1,
                                "zscore": zscore,
                                "correlation": correlation,
                                "strategy": "pairs_trading",
                                "pair_leg": "long",
                                "timestamp": datetime.utcnow()
                            }
                        ])
                    else:  # Symbol2 overpriced relative to Symbol1
                        # Buy Symbol1, Sell Symbol2
                        signals.extend([
                            {
                                "symbol": symbol1,
                                "action": "buy",
                                "signal_strength": min(abs(zscore) * 20, 100),
                                "entry_price": current_price1,
                                "pair_symbol": symbol2,
                                "zscore": zscore,
                                "correlation": correlation,
                                "strategy": "pairs_trading",
                                "pair_leg": "long",
                                "timestamp": datetime.utcnow()
                            },
                            {
                                "symbol": symbol2,
                                "action": "sell",
                                "signal_strength": min(abs(zscore) * 20, 100),
                                "entry_price": current_price2,
                                "pair_symbol": symbol1,
                                "zscore": zscore,
                                "correlation": correlation,
                                "strategy": "pairs_trading",
                                "pair_leg": "short",
                                "timestamp": datetime.utcnow()
                            }
                        ])

                # Generate exit signals
                elif abs(zscore) <= self.zscore_exit:
                    # Close existing positions
                    signals.extend([
                        {
                            "symbol": symbol1,
                            "action": "close",
                            "exit_price": current_price1,
                            "pair_symbol": symbol2,
                            "zscore": zscore,
                            "strategy": "pairs_trading",
                            "timestamp": datetime.utcnow()
                        },
                        {
                            "symbol": symbol2,
                            "action": "close",
                            "exit_price": current_price2,
                            "pair_symbol": symbol1,
                            "zscore": zscore,
                            "strategy": "pairs_trading",
                            "timestamp": datetime.utcnow()
                        }
                    ])

        except Exception as e:
            logger.error(f"Error generating pairs trading signals: {e}")

        return signals

    async def calculate_position_size(self, signal: Dict[str, Any], account_balance: float) -> int:
        """Calculate position size for pairs trading"""
        try:
            # Equal dollar amounts for both legs
            risk_per_leg = self.max_position_size
            entry_price = signal.get("entry_price", 0)

            if entry_price <= 0:
                return 0

            # Calculate position size
            max_investment = account_balance * risk_per_leg
            position_size = int(max_investment / entry_price)

            return max(1, position_size)

        except Exception as e:
            logger.error(f"Error calculating pairs trading position size: {e}")
            return 0


class AlgorithmicStrategyManager:
    """Manager for all algorithmic trading strategies"""

    def __init__(self):
        self.strategies: Dict[str, AlgorithmicStrategy] = {}
        self.active_strategies: Dict[int, AlgorithmicStrategy] = {}  # strategy_id -> strategy

    def register_strategy(self, strategy_type: str, strategy: AlgorithmicStrategy):
        """Register a new strategy type"""
        self.strategies[strategy_type] = strategy

    def create_strategy(self, strategy_type: str, parameters: Dict[str, Any]) -> Optional[AlgorithmicStrategy]:
        """Create a strategy instance"""
        try:
            if strategy_type == "momentum":
                return MomentumStrategy(parameters)
            elif strategy_type == "mean_reversion":
                return MeanReversionStrategy(parameters)
            elif strategy_type == "pairs_trading":
                return PairsTradingStrategy(parameters)
            else:
                logger.error(f"Unknown strategy type: {strategy_type}")
                return None

        except Exception as e:
            logger.error(f"Error creating strategy {strategy_type}: {e}")
            return None

    async def run_strategy(self, strategy_id: int, strategy_type: str, parameters: Dict[str, Any],
                          symbols: List[str], account_balance: float) -> List[Dict[str, Any]]:
        """Run a specific strategy and generate signals"""
        try:
            # Create strategy instance
            strategy = self.create_strategy(strategy_type, parameters)
            if not strategy:
                return []

            # Validate parameters
            if not strategy.validate_parameters():
                logger.error(f"Invalid parameters for strategy {strategy_type}")
                return []

            # Generate signals
            signals = await strategy.generate_signals(symbols)

            # Calculate position sizes
            for signal in signals:
                position_size = await strategy.calculate_position_size(signal, account_balance)
                signal["position_size"] = position_size
                signal["strategy_id"] = strategy_id

            return signals

        except Exception as e:
            logger.error(f"Error running strategy {strategy_type}: {e}")
            return []

    async def get_strategy_performance(self, strategy_id: int) -> Dict[str, Any]:
        """Get performance metrics for a strategy"""
        try:
            async with get_db() as db:
                # Get trades for this strategy
                result = await db.execute(
                    select(Trade).where(
                        and_(
                            Trade.strategy_id == strategy_id,
                            Trade.status == OrderStatus.FILLED
                        )
                    )
                )
                trades = result.scalars().all()

                if not trades:
                    return {"total_trades": 0, "total_pnl": 0.0, "win_rate": 0.0}

                # Calculate performance metrics
                total_trades = len(trades)
                winning_trades = sum(1 for trade in trades if trade.realized_pnl and trade.realized_pnl > 0)
                total_pnl = sum(trade.realized_pnl or 0 for trade in trades)
                win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0

                # Calculate average trade metrics
                avg_win = np.mean([trade.realized_pnl for trade in trades if trade.realized_pnl and trade.realized_pnl > 0]) if winning_trades > 0 else 0
                avg_loss = np.mean([trade.realized_pnl for trade in trades if trade.realized_pnl and trade.realized_pnl < 0]) if (total_trades - winning_trades) > 0 else 0

                return {
                    "total_trades": total_trades,
                    "winning_trades": winning_trades,
                    "losing_trades": total_trades - winning_trades,
                    "total_pnl": total_pnl,
                    "win_rate": win_rate,
                    "average_win": avg_win,
                    "average_loss": avg_loss,
                    "profit_factor": abs(avg_win / avg_loss) if avg_loss != 0 else 0,
                    "sharpe_ratio": self._calculate_sharpe_ratio(trades),
                }

        except Exception as e:
            logger.error(f"Error getting strategy performance: {e}")
            return {"error": str(e)}

    def _calculate_sharpe_ratio(self, trades: List[Trade]) -> float:
        """Calculate Sharpe ratio for trades"""
        try:
            if len(trades) < 2:
                return 0.0

            returns = [trade.realized_pnl or 0 for trade in trades]
            mean_return = np.mean(returns)
            std_return = np.std(returns)

            if std_return == 0:
                return 0.0

            # Assuming risk-free rate of 2% annually, adjusted for trade frequency
            risk_free_rate = 0.02 / 252  # Daily risk-free rate

            return (mean_return - risk_free_rate) / std_return

        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0

    def get_available_strategies(self) -> Dict[str, Dict[str, Any]]:
        """Get list of available algorithmic strategies"""
        return {
            "momentum": {
                "name": "Momentum Strategy",
                "description": "Trades based on price momentum and volume confirmation",
                "parameters": {
                    "lookback_period": {"type": "int", "default": 20, "min": 5, "max": 100},
                    "momentum_threshold": {"type": "float", "default": 0.05, "min": 0.01, "max": 0.20},
                    "rsi_oversold": {"type": "int", "default": 30, "min": 10, "max": 40},
                    "rsi_overbought": {"type": "int", "default": 70, "min": 60, "max": 90},
                    "volume_multiplier": {"type": "float", "default": 1.5, "min": 1.0, "max": 5.0},
                    "max_position_size": {"type": "float", "default": 0.05, "min": 0.01, "max": 0.20}
                }
            },
            "mean_reversion": {
                "name": "Mean Reversion Strategy",
                "description": "Trades based on price deviations from statistical mean",
                "parameters": {
                    "lookback_period": {"type": "int", "default": 20, "min": 10, "max": 100},
                    "std_dev_threshold": {"type": "float", "default": 2.0, "min": 1.0, "max": 4.0},
                    "rsi_oversold": {"type": "int", "default": 30, "min": 10, "max": 40},
                    "rsi_overbought": {"type": "int", "default": 70, "min": 60, "max": 90},
                    "bollinger_period": {"type": "int", "default": 20, "min": 10, "max": 50},
                    "max_position_size": {"type": "float", "default": 0.03, "min": 0.01, "max": 0.15}
                }
            },
            "pairs_trading": {
                "name": "Pairs Trading Strategy",
                "description": "Market-neutral strategy trading correlated stock pairs",
                "parameters": {
                    "lookback_period": {"type": "int", "default": 60, "min": 30, "max": 200},
                    "correlation_threshold": {"type": "float", "default": 0.8, "min": 0.5, "max": 0.99},
                    "zscore_entry": {"type": "float", "default": 2.0, "min": 1.0, "max": 4.0},
                    "zscore_exit": {"type": "float", "default": 0.5, "min": 0.1, "max": 1.5},
                    "max_position_size": {"type": "float", "default": 0.02, "min": 0.01, "max": 0.10},
                    "pairs": {"type": "array", "default": [], "description": "Array of symbol pairs"}
                }
            }
        }
