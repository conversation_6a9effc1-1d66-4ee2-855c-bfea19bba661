import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Alert,
  Skeleton,
  FormControl,
  InputLabel,
  Select,
  TextField,
  InputAdornment,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  MoreVert,
  Visibility,
  Add,
  Search,
  FilterList,
  Download,
  Share,
  Star,
  StarBorder,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { motion } from 'framer-motion';

interface ScreeningResult {
  id: number;
  strategy_id: number;
  stock_symbol: string;
  stock_name: string;
  scan_timestamp: string;
  score: number;
  signal_strength: string;
  entry_price: number;
  target_price: number;
  stop_loss_price: number;
  criteria_met: any;
  market_data: any;
}

const ScreeningResults: React.FC = () => {
  const [results, setResults] = useState<ScreeningResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [selectedStrategy, setSelectedStrategy] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedResult, setSelectedResult] = useState<ScreeningResult | null>(
    null
  );
  const [watchlist, setWatchlist] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchResults();
  }, [selectedStrategy, page, rowsPerPage]);

  const fetchResults = async () => {
    setIsLoading(true);
    try {
      // Mock data - in real app, fetch from API
      const mockResults: ScreeningResult[] = [
        {
          id: 1,
          strategy_id: 1,
          stock_symbol: 'AAPL',
          stock_name: 'Apple Inc.',
          scan_timestamp: new Date().toISOString(),
          score: 85.5,
          signal_strength: 'Strong Buy',
          entry_price: 150.25,
          target_price: 165.0,
          stop_loss_price: 142.5,
          criteria_met: { rsi: true, volume: true, price: true },
          market_data: { change: 2.5, changePercent: 1.69 },
        },
        {
          id: 2,
          strategy_id: 1,
          stock_symbol: 'MSFT',
          stock_name: 'Microsoft Corporation',
          scan_timestamp: new Date().toISOString(),
          score: 78.2,
          signal_strength: 'Buy',
          entry_price: 335.8,
          target_price: 355.0,
          stop_loss_price: 320.0,
          criteria_met: { rsi: true, volume: false, price: true },
          market_data: { change: -1.2, changePercent: -0.36 },
        },
        {
          id: 3,
          strategy_id: 2,
          stock_symbol: 'GOOGL',
          stock_name: 'Alphabet Inc.',
          scan_timestamp: new Date().toISOString(),
          score: 92.1,
          signal_strength: 'Strong Buy',
          entry_price: 2750.0,
          target_price: 2950.0,
          stop_loss_price: 2600.0,
          criteria_met: { rsi: true, volume: true, price: true },
          market_data: { change: 15.5, changePercent: 0.57 },
        },
      ];

      setResults(mockResults);
    } catch (error) {
      console.error('Failed to fetch results:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleMenuOpen = (
    event: React.MouseEvent<HTMLElement>,
    result: ScreeningResult
  ) => {
    setAnchorEl(event.currentTarget);
    setSelectedResult(result);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedResult(null);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleToggleWatchlist = (symbol: string) => {
    const newWatchlist = new Set(watchlist);
    if (newWatchlist.has(symbol)) {
      newWatchlist.delete(symbol);
    } else {
      newWatchlist.add(symbol);
    }
    setWatchlist(newWatchlist);
  };

  const getSignalColor = (signal: string) => {
    switch (signal.toLowerCase()) {
      case 'strong buy':
        return 'success';
      case 'buy':
        return 'primary';
      case 'hold':
        return 'warning';
      case 'sell':
        return 'error';
      default:
        return 'default';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'success.main';
    if (score >= 60) return 'warning.main';
    return 'error.main';
  };

  const filteredResults = results.filter(
    (result) =>
      result.stock_symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||
      result.stock_name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (isLoading) {
    return (
      <Box>
        {[1, 2, 3, 4, 5].map((index) => (
          <Skeleton
            key={index}
            variant="rectangular"
            height={60}
            sx={{ mb: 1 }}
          />
        ))}
      </Box>
    );
  }

  return (
    <Box>
      {/* Filters and Search */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, alignItems: 'center' }}>
        <FormControl size="small" sx={{ minWidth: 200 }}>
          <InputLabel>Strategy</InputLabel>
          <Select
            value={selectedStrategy}
            onChange={(e) => setSelectedStrategy(e.target.value)}
            label="Strategy"
          >
            <MenuItem value="all">All Strategies</MenuItem>
            <MenuItem value="1">Growth Stocks</MenuItem>
            <MenuItem value="2">Value Picks</MenuItem>
            <MenuItem value="3">Momentum Play</MenuItem>
          </Select>
        </FormControl>

        <TextField
          size="small"
          placeholder="Search symbols or companies..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
          sx={{ flexGrow: 1, maxWidth: 300 }}
        />

        <Button variant="outlined" startIcon={<FilterList />} size="small">
          Filters
        </Button>

        <Button variant="outlined" startIcon={<Download />} size="small">
          Export
        </Button>
      </Box>

      {/* Results Summary */}
      <Alert severity="info" sx={{ mb: 3 }}>
        Found {filteredResults.length} stocks matching your screening criteria
      </Alert>

      {/* Results Table */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Symbol</TableCell>
                <TableCell>Company</TableCell>
                <TableCell align="right">Score</TableCell>
                <TableCell>Signal</TableCell>
                <TableCell align="right">Entry Price</TableCell>
                <TableCell align="right">Target</TableCell>
                <TableCell align="right">Stop Loss</TableCell>
                <TableCell align="right">Change</TableCell>
                <TableCell>Scanned</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredResults
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((result, index) => (
                  <TableRow
                    key={result.id}
                    component={motion.tr}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    hover
                  >
                    <TableCell>
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                      >
                        <Typography variant="body2" fontWeight={600}>
                          {result.stock_symbol}
                        </Typography>
                        <IconButton
                          size="small"
                          onClick={() =>
                            handleToggleWatchlist(result.stock_symbol)
                          }
                        >
                          {watchlist.has(result.stock_symbol) ? (
                            <Star color="warning" fontSize="small" />
                          ) : (
                            <StarBorder fontSize="small" />
                          )}
                        </IconButton>
                      </Box>
                    </TableCell>

                    <TableCell>
                      <Typography variant="body2">
                        {result.stock_name}
                      </Typography>
                    </TableCell>

                    <TableCell align="right">
                      <Typography
                        variant="body2"
                        fontWeight={600}
                        sx={{ color: getScoreColor(result.score) }}
                      >
                        {result.score.toFixed(1)}
                      </Typography>
                    </TableCell>

                    <TableCell>
                      <Chip
                        label={result.signal_strength}
                        color={getSignalColor(result.signal_strength)}
                        size="small"
                      />
                    </TableCell>

                    <TableCell align="right">
                      <Typography variant="body2">
                        ${result.entry_price.toFixed(2)}
                      </Typography>
                    </TableCell>

                    <TableCell align="right">
                      <Typography variant="body2" color="success.main">
                        ${result.target_price.toFixed(2)}
                      </Typography>
                    </TableCell>

                    <TableCell align="right">
                      <Typography variant="body2" color="error.main">
                        ${result.stop_loss_price.toFixed(2)}
                      </Typography>
                    </TableCell>

                    <TableCell align="right">
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'flex-end',
                        }}
                      >
                        {result.market_data.change >= 0 ? (
                          <TrendingUp color="success" fontSize="small" />
                        ) : (
                          <TrendingDown color="error" fontSize="small" />
                        )}
                        <Typography
                          variant="body2"
                          sx={{
                            color:
                              result.market_data.change >= 0
                                ? 'success.main'
                                : 'error.main',
                            ml: 0.5,
                          }}
                        >
                          {result.market_data.changePercent.toFixed(2)}%
                        </Typography>
                      </Box>
                    </TableCell>

                    <TableCell>
                      <Typography variant="caption" color="text.secondary">
                        {format(
                          new Date(result.scan_timestamp),
                          'MMM dd, HH:mm'
                        )}
                      </Typography>
                    </TableCell>

                    <TableCell align="center">
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuOpen(e, result)}
                      >
                        <MoreVert />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={filteredResults.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Card>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem>
          <ListItemIcon>
            <Visibility fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Details</ListItemText>
        </MenuItem>

        <MenuItem>
          <ListItemIcon>
            <Add fontSize="small" />
          </ListItemIcon>
          <ListItemText>Add to Watchlist</ListItemText>
        </MenuItem>

        <MenuItem>
          <ListItemIcon>
            <Share fontSize="small" />
          </ListItemIcon>
          <ListItemText>Share</ListItemText>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default ScreeningResults;
