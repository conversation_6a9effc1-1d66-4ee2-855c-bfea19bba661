"""
Options trading models for contracts, strategies, and Greeks calculations
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, JSON, Enum, Index, ForeignKey, Numeric
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime, date
from typing import Dict, Any, Optional, List
from decimal import Decimal
import enum

from app.core.database import Base

class OptionType(enum.Enum):
    """Option type enumeration"""
    CALL = "call"
    PUT = "put"

class OptionStyle(enum.Enum):
    """Option style enumeration"""
    AMERICAN = "american"
    EUROPEAN = "european"

class OptionStatus(enum.Enum):
    """Option contract status"""
    ACTIVE = "active"
    EXPIRED = "expired"
    EXERCISED = "exercised"
    ASSIGNED = "assigned"

class StrategyType(enum.Enum):
    """Options strategy types"""
    SINGLE_LEG = "single_leg"
    COVERED_CALL = "covered_call"
    PROTECTIVE_PUT = "protective_put"
    STRADDLE = "straddle"
    STRANGLE = "strangle"
    IRON_CONDOR = "iron_condor"
    IRON_BUTTERFLY = "iron_butterfly"
    BUTTERFLY_SPREAD = "butterfly_spread"
    CALENDAR_SPREAD = "calendar_spread"
    DIAGONAL_SPREAD = "diagonal_spread"
    COLLAR = "collar"
    SYNTHETIC_LONG = "synthetic_long"
    SYNTHETIC_SHORT = "synthetic_short"

class OptionContract(Base):
    """Options contract model"""
    
    __tablename__ = "option_contracts"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign keys
    underlying_stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False, index=True)
    
    # Contract specifications
    symbol = Column(String(50), nullable=False, unique=True, index=True)  # e.g., AAPL240315C00150000
    option_type = Column(Enum(OptionType), nullable=False)
    strike_price = Column(Numeric(10, 2), nullable=False)
    expiration_date = Column(DateTime(timezone=True), nullable=False, index=True)
    
    # Contract details
    contract_size = Column(Integer, default=100)  # Standard is 100 shares
    option_style = Column(Enum(OptionStyle), default=OptionStyle.AMERICAN)
    status = Column(Enum(OptionStatus), default=OptionStatus.ACTIVE)
    
    # Market data
    last_price = Column(Numeric(10, 4))
    bid_price = Column(Numeric(10, 4))
    ask_price = Column(Numeric(10, 4))
    bid_size = Column(Integer)
    ask_size = Column(Integer)
    volume = Column(Integer, default=0)
    open_interest = Column(Integer, default=0)
    
    # Greeks
    delta = Column(Numeric(8, 6))
    gamma = Column(Numeric(8, 6))
    theta = Column(Numeric(8, 6))
    vega = Column(Numeric(8, 6))
    rho = Column(Numeric(8, 6))
    
    # Implied volatility and time value
    implied_volatility = Column(Numeric(8, 6))
    intrinsic_value = Column(Numeric(10, 4))
    time_value = Column(Numeric(10, 4))
    
    # Risk metrics
    probability_itm = Column(Numeric(5, 4))  # Probability in-the-money
    probability_profit = Column(Numeric(5, 4))  # Probability of profit
    
    # Timestamps
    last_updated = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    underlying_stock = relationship("Stock", back_populates="option_contracts")
    option_trades = relationship("OptionTrade", back_populates="option_contract")
    option_positions = relationship("OptionPosition", back_populates="option_contract")
    
    # Indexes
    __table_args__ = (
        Index('idx_option_underlying_expiry', 'underlying_stock_id', 'expiration_date'),
        Index('idx_option_type_strike', 'option_type', 'strike_price'),
        Index('idx_option_symbol', 'symbol'),
    )
    
    def __repr__(self):
        return f"<OptionContract(symbol='{self.symbol}', type='{self.option_type.value}', strike={self.strike_price})>"
    
    @property
    def is_call(self) -> bool:
        """Check if this is a call option"""
        return self.option_type == OptionType.CALL
    
    @property
    def is_put(self) -> bool:
        """Check if this is a put option"""
        return self.option_type == OptionType.PUT
    
    @property
    def is_itm(self) -> bool:
        """Check if option is in-the-money"""
        if not self.underlying_stock or not self.underlying_stock.current_price:
            return False
        
        current_price = float(self.underlying_stock.current_price)
        strike = float(self.strike_price)
        
        if self.is_call:
            return current_price > strike
        else:
            return current_price < strike
    
    @property
    def is_otm(self) -> bool:
        """Check if option is out-of-the-money"""
        return not self.is_itm
    
    @property
    def days_to_expiration(self) -> int:
        """Calculate days to expiration"""
        if not self.expiration_date:
            return 0
        return (self.expiration_date.date() - date.today()).days
    
    @property
    def time_to_expiration(self) -> float:
        """Calculate time to expiration in years"""
        days = self.days_to_expiration
        return days / 365.25 if days > 0 else 0.0
    
    def calculate_intrinsic_value(self, underlying_price: float) -> float:
        """Calculate intrinsic value"""
        strike = float(self.strike_price)
        
        if self.is_call:
            return max(0, underlying_price - strike)
        else:
            return max(0, strike - underlying_price)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert option contract to dictionary"""
        return {
            "id": self.id,
            "symbol": self.symbol,
            "option_type": self.option_type.value,
            "strike_price": float(self.strike_price) if self.strike_price else None,
            "expiration_date": self.expiration_date.isoformat() if self.expiration_date else None,
            "last_price": float(self.last_price) if self.last_price else None,
            "bid_price": float(self.bid_price) if self.bid_price else None,
            "ask_price": float(self.ask_price) if self.ask_price else None,
            "volume": self.volume,
            "open_interest": self.open_interest,
            "delta": float(self.delta) if self.delta else None,
            "gamma": float(self.gamma) if self.gamma else None,
            "theta": float(self.theta) if self.theta else None,
            "vega": float(self.vega) if self.vega else None,
            "rho": float(self.rho) if self.rho else None,
            "implied_volatility": float(self.implied_volatility) if self.implied_volatility else None,
            "intrinsic_value": float(self.intrinsic_value) if self.intrinsic_value else None,
            "time_value": float(self.time_value) if self.time_value else None,
            "days_to_expiration": self.days_to_expiration,
            "is_itm": self.is_itm,
            "probability_itm": float(self.probability_itm) if self.probability_itm else None,
        }


class OptionTrade(Base):
    """Options trade execution model"""

    __tablename__ = "option_trades"

    # Primary key
    id = Column(Integer, primary_key=True, index=True)

    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    option_contract_id = Column(Integer, ForeignKey("option_contracts.id"), nullable=False, index=True)
    broker_account_id = Column(Integer, ForeignKey("broker_accounts.id"), nullable=False, index=True)
    strategy_id = Column(Integer, ForeignKey("strategies.id"), index=True)
    option_strategy_id = Column(Integer, ForeignKey("option_strategies.id"), index=True)

    # Order information
    broker_order_id = Column(String(100), index=True)
    order_type = Column(String(20), nullable=False)  # market, limit, stop
    order_side = Column(String(20), nullable=False)  # buy_to_open, sell_to_open, buy_to_close, sell_to_close

    # Quantities and prices
    quantity = Column(Integer, nullable=False)  # Number of contracts
    price = Column(Numeric(10, 4))  # Premium per contract
    filled_quantity = Column(Integer, default=0)
    average_fill_price = Column(Numeric(10, 4))

    # Order status
    status = Column(String(20), default="pending")
    rejection_reason = Column(Text)
    time_in_force = Column(String(10), default="day")

    # Execution details
    submitted_at = Column(DateTime(timezone=True))
    filled_at = Column(DateTime(timezone=True))
    cancelled_at = Column(DateTime(timezone=True))

    # Financial details
    commission = Column(Numeric(10, 2), default=0.0)
    fees = Column(Numeric(10, 2), default=0.0)
    total_cost = Column(Numeric(12, 2))  # Total premium paid/received

    # Greeks at execution
    delta_at_execution = Column(Numeric(8, 6))
    gamma_at_execution = Column(Numeric(8, 6))
    theta_at_execution = Column(Numeric(8, 6))
    vega_at_execution = Column(Numeric(8, 6))
    implied_vol_at_execution = Column(Numeric(8, 6))

    # Paper trading flag
    is_paper_trade = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User")
    option_contract = relationship("OptionContract", back_populates="option_trades")
    broker_account = relationship("BrokerAccount")
    strategy = relationship("Strategy")
    option_strategy = relationship("OptionStrategy", back_populates="option_trades")

    # Indexes
    __table_args__ = (
        Index('idx_option_trade_user_date', 'user_id', 'created_at'),
        Index('idx_option_trade_contract', 'option_contract_id'),
        Index('idx_option_trade_status', 'status'),
    )

    def __repr__(self):
        return f"<OptionTrade(id={self.id}, contract={self.option_contract.symbol if self.option_contract else 'N/A'}, side='{self.order_side}', quantity={self.quantity})>"

    @property
    def is_opening_trade(self) -> bool:
        """Check if this is an opening trade"""
        return self.order_side in ["buy_to_open", "sell_to_open"]

    @property
    def is_closing_trade(self) -> bool:
        """Check if this is a closing trade"""
        return self.order_side in ["buy_to_close", "sell_to_close"]

    @property
    def is_long_position(self) -> bool:
        """Check if this creates/closes a long position"""
        return self.order_side in ["buy_to_open", "sell_to_close"]

    @property
    def is_short_position(self) -> bool:
        """Check if this creates/closes a short position"""
        return self.order_side in ["sell_to_open", "buy_to_close"]

    @property
    def total_premium(self) -> float:
        """Calculate total premium (price * quantity * contract_size)"""
        if not self.average_fill_price or not self.filled_quantity:
            return 0.0

        contract_size = self.option_contract.contract_size if self.option_contract else 100
        return float(self.average_fill_price) * self.filled_quantity * contract_size

    def to_dict(self) -> Dict[str, Any]:
        """Convert option trade to dictionary"""
        return {
            "id": self.id,
            "broker_order_id": self.broker_order_id,
            "order_type": self.order_type,
            "order_side": self.order_side,
            "quantity": self.quantity,
            "price": float(self.price) if self.price else None,
            "filled_quantity": self.filled_quantity,
            "average_fill_price": float(self.average_fill_price) if self.average_fill_price else None,
            "status": self.status,
            "commission": float(self.commission) if self.commission else None,
            "fees": float(self.fees) if self.fees else None,
            "total_cost": float(self.total_cost) if self.total_cost else None,
            "total_premium": self.total_premium,
            "is_paper_trade": self.is_paper_trade,
            "is_opening_trade": self.is_opening_trade,
            "is_closing_trade": self.is_closing_trade,
            "submitted_at": self.submitted_at.isoformat() if self.submitted_at else None,
            "filled_at": self.filled_at.isoformat() if self.filled_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }


class OptionPosition(Base):
    """Current options position model"""

    __tablename__ = "option_positions"

    # Primary key
    id = Column(Integer, primary_key=True, index=True)

    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    option_contract_id = Column(Integer, ForeignKey("option_contracts.id"), nullable=False, index=True)
    broker_account_id = Column(Integer, ForeignKey("broker_accounts.id"), nullable=False, index=True)
    option_strategy_id = Column(Integer, ForeignKey("option_strategies.id"), index=True)

    # Position details
    quantity = Column(Integer, nullable=False)  # Positive for long, negative for short
    average_cost = Column(Numeric(10, 4), nullable=False)  # Average premium paid/received

    # Current market value
    current_price = Column(Numeric(10, 4))
    market_value = Column(Numeric(12, 2))

    # P&L calculations
    unrealized_pnl = Column(Numeric(12, 2))
    unrealized_pnl_percent = Column(Numeric(8, 4))
    day_pnl = Column(Numeric(12, 2))
    day_pnl_percent = Column(Numeric(8, 4))

    # Greeks for position
    position_delta = Column(Numeric(10, 6))
    position_gamma = Column(Numeric(10, 6))
    position_theta = Column(Numeric(10, 6))
    position_vega = Column(Numeric(10, 6))
    position_rho = Column(Numeric(10, 6))

    # Risk metrics
    break_even_price = Column(Numeric(10, 2))
    max_profit = Column(Numeric(12, 2))
    max_loss = Column(Numeric(12, 2))
    probability_profit = Column(Numeric(5, 4))

    # Position metadata
    opened_at = Column(DateTime(timezone=True), nullable=False)
    last_updated = Column(DateTime(timezone=True), server_default=func.now())

    # Paper trading flag
    is_paper_position = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User")
    option_contract = relationship("OptionContract", back_populates="option_positions")
    broker_account = relationship("BrokerAccount")
    option_strategy = relationship("OptionStrategy", back_populates="option_positions")

    # Indexes
    __table_args__ = (
        Index('idx_option_position_user_contract', 'user_id', 'option_contract_id'),
        Index('idx_option_position_broker', 'broker_account_id'),
        Index('idx_option_position_strategy', 'option_strategy_id'),
    )

    def __repr__(self):
        return f"<OptionPosition(id={self.id}, contract={self.option_contract.symbol if self.option_contract else 'N/A'}, quantity={self.quantity})>"

    @property
    def is_long(self) -> bool:
        """Check if this is a long position"""
        return self.quantity > 0

    @property
    def is_short(self) -> bool:
        """Check if this is a short position"""
        return self.quantity < 0

    @property
    def total_cost(self) -> float:
        """Get total cost of position"""
        return float(self.average_cost) * abs(self.quantity) * 100  # Contract size

    @property
    def is_profitable(self) -> bool:
        """Check if position is currently profitable"""
        return self.unrealized_pnl is not None and float(self.unrealized_pnl) > 0

    def update_market_value(self, current_price: float):
        """Update position market value and P&L"""
        self.current_price = Decimal(str(current_price))
        contract_size = 100  # Standard contract size

        # Calculate market value
        self.market_value = Decimal(str(current_price)) * abs(self.quantity) * contract_size

        # Calculate unrealized P&L
        cost_basis = self.average_cost * abs(self.quantity) * contract_size

        if self.is_long:
            self.unrealized_pnl = self.market_value - cost_basis
        else:
            self.unrealized_pnl = cost_basis - self.market_value

        # Calculate percentage
        if cost_basis > 0:
            self.unrealized_pnl_percent = (self.unrealized_pnl / cost_basis) * 100

    def update_greeks(self, delta: float, gamma: float, theta: float, vega: float, rho: float):
        """Update position Greeks"""
        multiplier = self.quantity  # Positive for long, negative for short

        self.position_delta = Decimal(str(delta)) * multiplier
        self.position_gamma = Decimal(str(gamma)) * multiplier
        self.position_theta = Decimal(str(theta)) * multiplier
        self.position_vega = Decimal(str(vega)) * multiplier
        self.position_rho = Decimal(str(rho)) * multiplier

    def to_dict(self) -> Dict[str, Any]:
        """Convert option position to dictionary"""
        return {
            "id": self.id,
            "quantity": self.quantity,
            "average_cost": float(self.average_cost) if self.average_cost else None,
            "current_price": float(self.current_price) if self.current_price else None,
            "market_value": float(self.market_value) if self.market_value else None,
            "total_cost": self.total_cost,
            "unrealized_pnl": float(self.unrealized_pnl) if self.unrealized_pnl else None,
            "unrealized_pnl_percent": float(self.unrealized_pnl_percent) if self.unrealized_pnl_percent else None,
            "day_pnl": float(self.day_pnl) if self.day_pnl else None,
            "position_delta": float(self.position_delta) if self.position_delta else None,
            "position_gamma": float(self.position_gamma) if self.position_gamma else None,
            "position_theta": float(self.position_theta) if self.position_theta else None,
            "position_vega": float(self.position_vega) if self.position_vega else None,
            "break_even_price": float(self.break_even_price) if self.break_even_price else None,
            "max_profit": float(self.max_profit) if self.max_profit else None,
            "max_loss": float(self.max_loss) if self.max_loss else None,
            "probability_profit": float(self.probability_profit) if self.probability_profit else None,
            "is_long": self.is_long,
            "is_short": self.is_short,
            "is_profitable": self.is_profitable,
            "is_paper_position": self.is_paper_position,
            "opened_at": self.opened_at.isoformat() if self.opened_at else None,
        }


class OptionStrategy(Base):
    """Multi-leg options strategy model"""

    __tablename__ = "option_strategies"

    # Primary key
    id = Column(Integer, primary_key=True, index=True)

    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    broker_account_id = Column(Integer, ForeignKey("broker_accounts.id"), nullable=False, index=True)
    underlying_stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False, index=True)

    # Strategy information
    name = Column(String(100), nullable=False)
    strategy_type = Column(Enum(StrategyType), nullable=False)
    description = Column(Text)

    # Strategy configuration
    legs = Column(JSON, nullable=False)  # Array of strategy legs
    entry_criteria = Column(JSON)  # Entry conditions
    exit_criteria = Column(JSON)  # Exit conditions

    # Risk parameters
    max_loss = Column(Numeric(12, 2))
    max_profit = Column(Numeric(12, 2))
    break_even_points = Column(JSON)  # Array of break-even prices

    # Position sizing
    quantity = Column(Integer, default=1)  # Number of strategy units
    capital_allocated = Column(Numeric(12, 2))

    # Current status
    status = Column(String(20), default="pending")  # pending, active, closed, expired

    # Execution details
    opened_at = Column(DateTime(timezone=True))
    closed_at = Column(DateTime(timezone=True))
    expires_at = Column(DateTime(timezone=True))

    # Performance tracking
    total_cost = Column(Numeric(12, 2))  # Net debit/credit
    current_value = Column(Numeric(12, 2))
    unrealized_pnl = Column(Numeric(12, 2))
    realized_pnl = Column(Numeric(12, 2))

    # Greeks for entire strategy
    strategy_delta = Column(Numeric(10, 6))
    strategy_gamma = Column(Numeric(10, 6))
    strategy_theta = Column(Numeric(10, 6))
    strategy_vega = Column(Numeric(10, 6))
    strategy_rho = Column(Numeric(10, 6))

    # Risk metrics
    probability_profit = Column(Numeric(5, 4))
    days_to_expiration = Column(Integer)

    # Paper trading flag
    is_paper_strategy = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User")
    broker_account = relationship("BrokerAccount")
    underlying_stock = relationship("Stock")
    option_trades = relationship("OptionTrade", back_populates="option_strategy")
    option_positions = relationship("OptionPosition", back_populates="option_strategy")

    # Indexes
    __table_args__ = (
        Index('idx_option_strategy_user', 'user_id'),
        Index('idx_option_strategy_type', 'strategy_type'),
        Index('idx_option_strategy_status', 'status'),
        Index('idx_option_strategy_underlying', 'underlying_stock_id'),
    )

    def __repr__(self):
        return f"<OptionStrategy(id={self.id}, name='{self.name}', type='{self.strategy_type.value}', status='{self.status}')>"

    @property
    def is_active(self) -> bool:
        """Check if strategy is currently active"""
        return self.status == "active"

    @property
    def is_profitable(self) -> bool:
        """Check if strategy is currently profitable"""
        return self.unrealized_pnl is not None and float(self.unrealized_pnl) > 0

    @property
    def total_premium_paid(self) -> float:
        """Calculate total premium paid for the strategy"""
        if not self.total_cost:
            return 0.0
        return float(self.total_cost) if float(self.total_cost) > 0 else 0.0

    @property
    def total_premium_received(self) -> float:
        """Calculate total premium received for the strategy"""
        if not self.total_cost:
            return 0.0
        return abs(float(self.total_cost)) if float(self.total_cost) < 0 else 0.0

    @property
    def is_net_debit(self) -> bool:
        """Check if strategy is a net debit"""
        return self.total_cost is not None and float(self.total_cost) > 0

    @property
    def is_net_credit(self) -> bool:
        """Check if strategy is a net credit"""
        return self.total_cost is not None and float(self.total_cost) < 0

    def calculate_pnl(self) -> Dict[str, float]:
        """Calculate comprehensive P&L for the strategy"""
        if not self.current_value or not self.total_cost:
            return {"unrealized_pnl": 0.0, "unrealized_pnl_percent": 0.0}

        current_val = float(self.current_value)
        cost_basis = float(self.total_cost)

        # For net debit strategies: P&L = current_value - cost_basis
        # For net credit strategies: P&L = cost_basis - current_value
        if self.is_net_debit:
            unrealized_pnl = current_val - cost_basis
        else:
            unrealized_pnl = abs(cost_basis) - current_val

        # Calculate percentage
        pnl_percent = 0.0
        if cost_basis != 0:
            pnl_percent = (unrealized_pnl / abs(cost_basis)) * 100

        return {
            "unrealized_pnl": unrealized_pnl,
            "unrealized_pnl_percent": pnl_percent
        }

    def to_dict(self) -> Dict[str, Any]:
        """Convert option strategy to dictionary"""
        pnl_data = self.calculate_pnl()

        return {
            "id": self.id,
            "name": self.name,
            "strategy_type": self.strategy_type.value,
            "description": self.description,
            "legs": self.legs,
            "quantity": self.quantity,
            "status": self.status,
            "total_cost": float(self.total_cost) if self.total_cost else None,
            "current_value": float(self.current_value) if self.current_value else None,
            "unrealized_pnl": pnl_data["unrealized_pnl"],
            "unrealized_pnl_percent": pnl_data["unrealized_pnl_percent"],
            "realized_pnl": float(self.realized_pnl) if self.realized_pnl else None,
            "max_profit": float(self.max_profit) if self.max_profit else None,
            "max_loss": float(self.max_loss) if self.max_loss else None,
            "break_even_points": self.break_even_points,
            "strategy_delta": float(self.strategy_delta) if self.strategy_delta else None,
            "strategy_gamma": float(self.strategy_gamma) if self.strategy_gamma else None,
            "strategy_theta": float(self.strategy_theta) if self.strategy_theta else None,
            "strategy_vega": float(self.strategy_vega) if self.strategy_vega else None,
            "probability_profit": float(self.probability_profit) if self.probability_profit else None,
            "days_to_expiration": self.days_to_expiration,
            "is_active": self.is_active,
            "is_profitable": self.is_profitable,
            "is_net_debit": self.is_net_debit,
            "is_net_credit": self.is_net_credit,
            "is_paper_strategy": self.is_paper_strategy,
            "opened_at": self.opened_at.isoformat() if self.opened_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }
