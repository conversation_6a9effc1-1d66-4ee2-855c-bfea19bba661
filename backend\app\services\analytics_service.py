"""
Analytics and Monitoring Service
Tracks API usage, performance metrics, and user behavior
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum

from app.core.database import get_redis
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


class EventType(str, Enum):
    """Types of events to track"""
    API_REQUEST = "api_request"
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    SUBSCRIPTION_UPGRADE = "subscription_upgrade"
    FEATURE_USAGE = "feature_usage"
    ERROR_OCCURRED = "error_occurred"
    RATE_LIMIT_HIT = "rate_limit_hit"
    PERFORMANCE_METRIC = "performance_metric"


@dataclass
class AnalyticsEvent:
    """Analytics event data structure"""
    event_type: EventType
    user_id: Optional[int]
    timestamp: datetime
    data: Dict[str, Any]
    session_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    subscription_tier: Optional[str] = None


class AnalyticsService:
    """Service for collecting and analyzing usage analytics"""
    
    def __init__(self):
        self.redis_key_prefix = "analytics"
        self.batch_size = 100
        self.flush_interval = 60  # seconds
        self._event_buffer: List[AnalyticsEvent] = []
        self._last_flush = time.time()
    
    async def track_event(
        self,
        event_type: EventType,
        user_id: Optional[int] = None,
        data: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        subscription_tier: Optional[str] = None
    ):
        """Track an analytics event"""
        event = AnalyticsEvent(
            event_type=event_type,
            user_id=user_id,
            timestamp=datetime.utcnow(),
            data=data or {},
            session_id=session_id,
            ip_address=ip_address,
            user_agent=user_agent,
            subscription_tier=subscription_tier
        )
        
        # Add to buffer
        self._event_buffer.append(event)
        
        # Flush if buffer is full or enough time has passed
        if (len(self._event_buffer) >= self.batch_size or 
            time.time() - self._last_flush >= self.flush_interval):
            await self._flush_events()
    
    async def track_api_request(
        self,
        endpoint: str,
        method: str,
        status_code: int,
        response_time: float,
        user_id: Optional[int] = None,
        subscription_tier: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """Track API request metrics"""
        await self.track_event(
            event_type=EventType.API_REQUEST,
            user_id=user_id,
            data={
                "endpoint": endpoint,
                "method": method,
                "status_code": status_code,
                "response_time": response_time,
                "success": 200 <= status_code < 400
            },
            ip_address=ip_address,
            user_agent=user_agent,
            subscription_tier=subscription_tier
        )
    
    async def track_feature_usage(
        self,
        feature_name: str,
        user_id: int,
        subscription_tier: str,
        usage_data: Optional[Dict[str, Any]] = None
    ):
        """Track feature usage"""
        await self.track_event(
            event_type=EventType.FEATURE_USAGE,
            user_id=user_id,
            data={
                "feature_name": feature_name,
                "usage_data": usage_data or {}
            },
            subscription_tier=subscription_tier
        )
    
    async def track_error(
        self,
        error_type: str,
        error_message: str,
        endpoint: Optional[str] = None,
        user_id: Optional[int] = None,
        subscription_tier: Optional[str] = None,
        stack_trace: Optional[str] = None
    ):
        """Track error occurrences"""
        await self.track_event(
            event_type=EventType.ERROR_OCCURRED,
            user_id=user_id,
            data={
                "error_type": error_type,
                "error_message": error_message,
                "endpoint": endpoint,
                "stack_trace": stack_trace
            },
            subscription_tier=subscription_tier
        )
    
    async def track_rate_limit_hit(
        self,
        user_id: int,
        subscription_tier: str,
        endpoint: str,
        limit_type: str,
        current_usage: int,
        limit: int
    ):
        """Track rate limit violations"""
        await self.track_event(
            event_type=EventType.RATE_LIMIT_HIT,
            user_id=user_id,
            data={
                "endpoint": endpoint,
                "limit_type": limit_type,
                "current_usage": current_usage,
                "limit": limit,
                "usage_percentage": (current_usage / limit) * 100 if limit > 0 else 0
            },
            subscription_tier=subscription_tier
        )
    
    async def _flush_events(self):
        """Flush events to Redis"""
        if not self._event_buffer:
            return
        
        try:
            redis_client = await get_redis()
            
            # Prepare events for storage
            events_data = []
            for event in self._event_buffer:
                event_dict = asdict(event)
                event_dict['timestamp'] = event.timestamp.isoformat()
                events_data.append(json.dumps(event_dict))
            
            # Store in Redis with expiration
            pipe = redis_client.pipeline()
            
            # Store individual events
            for i, event_data in enumerate(events_data):
                key = f"{self.redis_key_prefix}:events:{int(time.time())}:{i}"
                pipe.setex(key, 86400 * 7, event_data)  # Keep for 7 days
            
            # Update counters and aggregates
            await self._update_aggregates(pipe, self._event_buffer)
            
            await pipe.execute()
            
            # Clear buffer
            self._event_buffer.clear()
            self._last_flush = time.time()
            
        except Exception as e:
            logger.error(f"Failed to flush analytics events: {e}")
    
    async def _update_aggregates(self, pipe, events: List[AnalyticsEvent]):
        """Update aggregate statistics"""
        current_hour = datetime.utcnow().replace(minute=0, second=0, microsecond=0)
        current_day = current_hour.replace(hour=0)
        
        for event in events:
            # Hourly aggregates
            hour_key = f"{self.redis_key_prefix}:hourly:{current_hour.isoformat()}"
            pipe.hincrby(hour_key, f"{event.event_type.value}:count", 1)
            pipe.expire(hour_key, 86400 * 30)  # Keep for 30 days
            
            # Daily aggregates
            day_key = f"{self.redis_key_prefix}:daily:{current_day.isoformat()}"
            pipe.hincrby(day_key, f"{event.event_type.value}:count", 1)
            pipe.expire(day_key, 86400 * 365)  # Keep for 1 year
            
            # User-specific aggregates
            if event.user_id:
                user_key = f"{self.redis_key_prefix}:user:{event.user_id}:daily:{current_day.isoformat()}"
                pipe.hincrby(user_key, f"{event.event_type.value}:count", 1)
                pipe.expire(user_key, 86400 * 90)  # Keep for 90 days
            
            # Subscription tier aggregates
            if event.subscription_tier:
                tier_key = f"{self.redis_key_prefix}:tier:{event.subscription_tier}:daily:{current_day.isoformat()}"
                pipe.hincrby(tier_key, f"{event.event_type.value}:count", 1)
                pipe.expire(tier_key, 86400 * 365)  # Keep for 1 year
    
    async def get_usage_stats(
        self,
        user_id: Optional[int] = None,
        subscription_tier: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        granularity: str = "daily"
    ) -> Dict[str, Any]:
        """Get usage statistics"""
        try:
            redis_client = await get_redis()
            
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()
            
            stats = {
                "period": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat(),
                    "granularity": granularity
                },
                "data": {}
            }
            
            # Generate date range
            current_date = start_date
            while current_date <= end_date:
                if granularity == "hourly":
                    key_suffix = current_date.isoformat()
                    current_date += timedelta(hours=1)
                else:  # daily
                    key_suffix = current_date.replace(hour=0, minute=0, second=0, microsecond=0).isoformat()
                    current_date += timedelta(days=1)
                
                # Build Redis key based on filters
                if user_id:
                    redis_key = f"{self.redis_key_prefix}:user:{user_id}:{granularity}:{key_suffix}"
                elif subscription_tier:
                    redis_key = f"{self.redis_key_prefix}:tier:{subscription_tier}:{granularity}:{key_suffix}"
                else:
                    redis_key = f"{self.redis_key_prefix}:{granularity}:{key_suffix}"
                
                # Get data from Redis
                data = await redis_client.hgetall(redis_key)
                if data:
                    stats["data"][key_suffix] = {
                        k.decode(): int(v.decode()) for k, v in data.items()
                    }
                else:
                    stats["data"][key_suffix] = {}
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get usage stats: {e}")
            return {"error": str(e)}
    
    async def get_real_time_metrics(self) -> Dict[str, Any]:
        """Get real-time system metrics"""
        try:
            redis_client = await get_redis()
            current_hour = datetime.utcnow().replace(minute=0, second=0, microsecond=0)
            
            # Get current hour stats
            hour_key = f"{self.redis_key_prefix}:hourly:{current_hour.isoformat()}"
            hour_data = await redis_client.hgetall(hour_key)
            
            metrics = {
                "timestamp": datetime.utcnow().isoformat(),
                "current_hour_stats": {
                    k.decode(): int(v.decode()) for k, v in hour_data.items()
                } if hour_data else {},
                "active_users": await self._get_active_users_count(),
                "system_health": "healthy"  # Would implement actual health checks
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to get real-time metrics: {e}")
            return {"error": str(e)}
    
    async def _get_active_users_count(self) -> int:
        """Get count of active users in the last hour"""
        try:
            redis_client = await get_redis()
            # This would typically track active sessions
            # For now, return a placeholder
            return 0
        except Exception:
            return 0


# Global analytics service instance
analytics_service = AnalyticsService()


# Middleware helper function
async def track_request_analytics(
    endpoint: str,
    method: str,
    status_code: int,
    response_time: float,
    user_id: Optional[int] = None,
    subscription_tier: Optional[str] = None,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None
):
    """Helper function to track request analytics"""
    await analytics_service.track_api_request(
        endpoint=endpoint,
        method=method,
        status_code=status_code,
        response_time=response_time,
        user_id=user_id,
        subscription_tier=subscription_tier,
        ip_address=ip_address,
        user_agent=user_agent
    )
