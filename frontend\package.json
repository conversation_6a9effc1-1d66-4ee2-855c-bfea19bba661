{"name": "markethawk-frontend", "version": "1.0.0", "description": "MarketHawk - Advanced Stock Screener & Automated Trading Platform Frontend", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-charts": "^6.18.1", "@mui/x-data-grid": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "@reduxjs/toolkit": "^2.0.1", "@tanstack/react-query": "^5.8.4", "@types/node": "^20.10.0", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "axios": "^1.6.2", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "lightweight-charts": "^4.1.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "react-scripts": "^5.0.1", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "typescript": "^4.9.5", "web-vitals": "^3.5.0", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest", "prettier"], "plugins": ["prettier"], "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": "warn", "react-hooks/exhaustive-deps": "warn"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "overrides": {"nth-check": "^2.1.1", "postcss": "^8.4.31", "svgo": "^3.0.0", "webpack-dev-server": "^4.15.1"}, "proxy": "http://localhost:8000"}