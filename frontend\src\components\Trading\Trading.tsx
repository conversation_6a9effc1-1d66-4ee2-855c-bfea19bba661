import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Tabs,
  Tab,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Add,
  Cancel,
  Refresh,
  Settings,
  Warning,
  CheckCircle,
  Schedule,
  PlayArrow,
  Stop,
} from '@mui/icons-material';
import { useSelector, useDispatch } from 'react-redux';
import { motion } from 'framer-motion';
import { RootState } from '../../store/store';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`trading-tabpanel-${index}`}
      aria-labelledby={`trading-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

interface Order {
  id: string;
  symbol: string;
  side: 'buy' | 'sell';
  type: 'market' | 'limit' | 'stop';
  quantity: number;
  price?: number;
  status: 'pending' | 'filled' | 'cancelled' | 'rejected';
  timestamp: string;
  strategy?: string;
}

interface Position {
  symbol: string;
  quantity: number;
  avgPrice: number;
  currentPrice: number;
  unrealizedPnL: number;
  unrealizedPnLPercent: number;
  marketValue: number;
}

const Trading: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [orderDialogOpen, setOrderDialogOpen] = useState(false);
  const [orders, setOrders] = useState<Order[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [paperTrading, setPaperTrading] = useState(true);

  const dispatch = useDispatch();
  const { user } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    fetchTradingData();
  }, []);

  const fetchTradingData = async () => {
    setIsLoading(true);
    try {
      // Mock data - in real app, fetch from API
      const mockOrders: Order[] = [
        {
          id: '1',
          symbol: 'AAPL',
          side: 'buy',
          type: 'limit',
          quantity: 100,
          price: 150.0,
          status: 'filled',
          timestamp: new Date().toISOString(),
          strategy: 'Growth Strategy',
        },
        {
          id: '2',
          symbol: 'MSFT',
          side: 'sell',
          type: 'market',
          quantity: 50,
          status: 'pending',
          timestamp: new Date().toISOString(),
        },
      ];

      const mockPositions: Position[] = [
        {
          symbol: 'AAPL',
          quantity: 100,
          avgPrice: 148.5,
          currentPrice: 152.25,
          unrealizedPnL: 375.0,
          unrealizedPnLPercent: 2.52,
          marketValue: 15225.0,
        },
        {
          symbol: 'GOOGL',
          quantity: 10,
          avgPrice: 2750.0,
          currentPrice: 2785.5,
          unrealizedPnL: 355.0,
          unrealizedPnLPercent: 1.29,
          marketValue: 27855.0,
        },
      ];

      setOrders(mockOrders);
      setPositions(mockPositions);
    } catch (error) {
      console.error('Failed to fetch trading data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleCreateOrder = () => {
    setOrderDialogOpen(true);
  };

  const handleCloseOrderDialog = () => {
    setOrderDialogOpen(false);
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case 'filled':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'default';
      case 'rejected':
        return 'error';
      default:
        return 'default';
    }
  };

  const getOrderStatusIcon = (status: string) => {
    switch (status) {
      case 'filled':
        return <CheckCircle color="success" fontSize="small" />;
      case 'pending':
        return <Schedule color="warning" fontSize="small" />;
      case 'cancelled':
        return <Cancel color="action" fontSize="small" />;
      case 'rejected':
        return <Warning color="error" fontSize="small" />;
      default:
        return null;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  const totalPortfolioValue = positions.reduce(
    (sum, pos) => sum + pos.marketValue,
    0
  );
  const totalUnrealizedPnL = positions.reduce(
    (sum, pos) => sum + pos.unrealizedPnL,
    0
  );
  const totalUnrealizedPnLPercent =
    totalPortfolioValue > 0
      ? (totalUnrealizedPnL / (totalPortfolioValue - totalUnrealizedPnL)) * 100
      : 0;

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2,
          }}
        >
          <Typography variant="h4" component="h1" fontWeight={600}>
            Trading
          </Typography>

          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <FormControlLabel
              control={
                <Switch
                  checked={paperTrading}
                  onChange={(e) => setPaperTrading(e.target.checked)}
                  color="warning"
                />
              }
              label="Paper Trading"
            />

            <Tooltip title="Refresh Data">
              <IconButton color="primary" onClick={fetchTradingData}>
                <Refresh />
              </IconButton>
            </Tooltip>

            <Tooltip title="Trading Settings">
              <IconButton color="primary">
                <Settings />
              </IconButton>
            </Tooltip>

            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={handleCreateOrder}
            >
              New Order
            </Button>
          </Box>
        </Box>

        <Typography variant="body1" color="text.secondary">
          Manage your trades and monitor positions
        </Typography>
      </Box>

      {/* Paper Trading Alert */}
      {paperTrading && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Paper Trading Mode:</strong> All trades are simulated. No real
          money is involved.
        </Alert>
      )}

      {/* Portfolio Summary */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Total Portfolio Value
              </Typography>
              <Typography variant="h5" fontWeight={600}>
                {formatCurrency(totalPortfolioValue)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Unrealized P&L
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography
                  variant="h5"
                  fontWeight={600}
                  sx={{
                    color:
                      totalUnrealizedPnL >= 0 ? 'success.main' : 'error.main',
                  }}
                >
                  {formatCurrency(totalUnrealizedPnL)}
                </Typography>
                {totalUnrealizedPnL >= 0 ? (
                  <TrendingUp color="success" />
                ) : (
                  <TrendingDown color="error" />
                )}
              </Box>
              <Typography
                variant="body2"
                sx={{
                  color:
                    totalUnrealizedPnL >= 0 ? 'success.main' : 'error.main',
                }}
              >
                {totalUnrealizedPnL >= 0 ? '+' : ''}
                {totalUnrealizedPnLPercent.toFixed(2)}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Active Positions
              </Typography>
              <Typography variant="h5" fontWeight={600}>
                {positions.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Main Content */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            aria-label="trading tabs"
          >
            <Tab
              label="Positions"
              id="trading-tab-0"
              aria-controls="trading-tabpanel-0"
            />
            <Tab
              label="Orders"
              id="trading-tab-1"
              aria-controls="trading-tabpanel-1"
            />
            <Tab
              label="Trade History"
              id="trading-tab-2"
              aria-controls="trading-tabpanel-2"
            />
          </Tabs>
        </Box>

        <TabPanel value={activeTab} index={0}>
          {/* Positions Table */}
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Symbol</TableCell>
                  <TableCell align="right">Quantity</TableCell>
                  <TableCell align="right">Avg Price</TableCell>
                  <TableCell align="right">Current Price</TableCell>
                  <TableCell align="right">Market Value</TableCell>
                  <TableCell align="right">Unrealized P&L</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {positions.map((position, index) => (
                  <TableRow
                    key={position.symbol}
                    component={motion.tr}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <TableCell>
                      <Typography variant="body2" fontWeight={600}>
                        {position.symbol}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">{position.quantity}</TableCell>
                    <TableCell align="right">
                      {formatCurrency(position.avgPrice)}
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrency(position.currentPrice)}
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrency(position.marketValue)}
                    </TableCell>
                    <TableCell align="right">
                      <Box>
                        <Typography
                          variant="body2"
                          sx={{
                            color:
                              position.unrealizedPnL >= 0
                                ? 'success.main'
                                : 'error.main',
                          }}
                        >
                          {formatCurrency(position.unrealizedPnL)}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            color:
                              position.unrealizedPnL >= 0
                                ? 'success.main'
                                : 'error.main',
                          }}
                        >
                          ({position.unrealizedPnL >= 0 ? '+' : ''}
                          {position.unrealizedPnLPercent.toFixed(2)}%)
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Button size="small" variant="outlined" color="error">
                        Close
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          {/* Orders Table */}
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Symbol</TableCell>
                  <TableCell>Side</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell align="right">Quantity</TableCell>
                  <TableCell align="right">Price</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Strategy</TableCell>
                  <TableCell>Time</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {orders.map((order, index) => (
                  <TableRow
                    key={order.id}
                    component={motion.tr}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <TableCell>
                      <Typography variant="body2" fontWeight={600}>
                        {order.symbol}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={order.side.toUpperCase()}
                        color={order.side === 'buy' ? 'success' : 'error'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{order.type.toUpperCase()}</TableCell>
                    <TableCell align="right">{order.quantity}</TableCell>
                    <TableCell align="right">
                      {order.price ? formatCurrency(order.price) : 'Market'}
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                      >
                        {getOrderStatusIcon(order.status)}
                        <Chip
                          label={order.status.toUpperCase()}
                          color={getOrderStatusColor(order.status)}
                          size="small"
                        />
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="caption" color="text.secondary">
                        {order.strategy || 'Manual'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(order.timestamp).toLocaleTimeString()}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      {order.status === 'pending' && (
                        <IconButton size="small" color="error">
                          <Cancel />
                        </IconButton>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h6" color="text.secondary">
              Trade History
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              View your complete trading history and performance analytics
            </Typography>
            <Button variant="outlined" sx={{ mt: 2 }}>
              Coming Soon
            </Button>
          </Box>
        </TabPanel>
      </Card>

      {/* Order Dialog */}
      <Dialog
        open={orderDialogOpen}
        onClose={handleCloseOrderDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Create New Order</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField fullWidth label="Symbol" placeholder="e.g., AAPL" />
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth>
                  <InputLabel>Side</InputLabel>
                  <Select defaultValue="buy">
                    <MenuItem value="buy">Buy</MenuItem>
                    <MenuItem value="sell">Sell</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth>
                  <InputLabel>Type</InputLabel>
                  <Select defaultValue="market">
                    <MenuItem value="market">Market</MenuItem>
                    <MenuItem value="limit">Limit</MenuItem>
                    <MenuItem value="stop">Stop</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <TextField fullWidth label="Quantity" type="number" />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Price"
                  type="number"
                  placeholder="Leave empty for market orders"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseOrderDialog}>Cancel</Button>
          <Button variant="contained" onClick={handleCloseOrderDialog}>
            Place Order
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Trading;
