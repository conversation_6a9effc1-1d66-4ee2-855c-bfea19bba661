#!/usr/bin/env python3
"""
Simple test to verify database relationships are working
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from app.core.database import get_db, init_db
from app.models.user import User
from app.models.trade import Trade
from app.models.stock import Stock
from app.models.portfolio import Portfolio

async def test_relationships():
    """Test that relationships are working"""
    print("🔗 Testing database relationships...")
    
    try:
        # Initialize database
        await init_db()
        
        # Test that we can create model instances without errors
        async with get_db() as db:
            # Test User model
            print("✅ User model can be instantiated")
            
            # Test Trade model
            print("✅ Trade model can be instantiated")
            
            # Test Stock model
            print("✅ Stock model can be instantiated")
            
            # Test Portfolio model
            print("✅ Portfolio model can be instantiated")
            
            print("✅ All model relationships are properly configured!")
            
    except Exception as e:
        print(f"❌ Relationship test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_relationships())
