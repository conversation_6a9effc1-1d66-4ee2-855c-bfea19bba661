import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Tabs,
  Tab,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Add,
  PlayArrow,
  Stop,
  Refresh,
  Settings,
  TrendingUp,
  Assessment,
  FilterList,
} from '@mui/icons-material';
import { useSelector, useDispatch } from 'react-redux';
import { motion } from 'framer-motion';
import { RootState } from '../../store/store';
import StrategyBuilder from './StrategyBuilder';
import StrategyList from './StrategyList';
import ScreeningResults from './ScreeningResults';
import MarketOverview from './MarketOverview';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`screener-tabpanel-${index}`}
      aria-labelledby={`screener-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

const Screener: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [strategyDialogOpen, setStrategyDialogOpen] = useState(false);
  const [selectedStrategy, setSelectedStrategy] = useState<any>(null);
  const [isRunning, setIsRunning] = useState(false);

  const dispatch = useDispatch();
  const { user } = useSelector((state: RootState) => state.auth);
  const { strategies, isLoading, error } = useSelector(
    (state: RootState) => state.screening
  );

  useEffect(() => {
    // Fetch strategies on component mount
    // dispatch(fetchStrategies());
  }, [dispatch]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleCreateStrategy = () => {
    setSelectedStrategy(null);
    setStrategyDialogOpen(true);
  };

  const handleEditStrategy = (strategy: any) => {
    setSelectedStrategy(strategy);
    setStrategyDialogOpen(true);
  };

  const handleCloseStrategyDialog = () => {
    setStrategyDialogOpen(false);
    setSelectedStrategy(null);
  };

  const handleRunStrategy = async (strategyId: number) => {
    setIsRunning(true);
    try {
      // dispatch(runScreening(strategyId));
      // Show success notification
    } catch (error) {
      // Show error notification
    } finally {
      setIsRunning(false);
    }
  };

  const handleStopScreening = () => {
    setIsRunning(false);
    // dispatch(stopScreening());
  };

  const getSubscriptionLimits = () => {
    switch (user?.subscription_tier) {
      case 'enterprise':
        return {
          strategies: 'Unlimited',
          screenings: 'Unlimited',
          symbols: '8000+',
        };
      case 'premium':
        return { strategies: '20', screenings: '1000/hour', symbols: '2000' };
      default:
        return { strategies: '3', screenings: '100/hour', symbols: '500' };
    }
  };

  const limits = getSubscriptionLimits();

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2,
          }}
        >
          <Typography variant="h4" component="h1" fontWeight={600}>
            Stock Screener
          </Typography>

          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            {/* Subscription Info */}
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Chip
                label={`${limits.strategies} Strategies`}
                size="small"
                color="primary"
                variant="outlined"
              />
              <Chip
                label={`${limits.screenings} Screenings`}
                size="small"
                color="secondary"
                variant="outlined"
              />
              <Chip
                label={`${limits.symbols} Symbols`}
                size="small"
                color="info"
                variant="outlined"
              />
            </Box>

            {/* Action Buttons */}
            <Tooltip title="Refresh Data">
              <IconButton color="primary">
                <Refresh />
              </IconButton>
            </Tooltip>

            <Tooltip title="Screening Settings">
              <IconButton color="primary">
                <Settings />
              </IconButton>
            </Tooltip>

            {isRunning ? (
              <Button
                variant="contained"
                color="error"
                startIcon={<Stop />}
                onClick={handleStopScreening}
              >
                Stop Screening
              </Button>
            ) : (
              <Button
                variant="contained"
                startIcon={<PlayArrow />}
                onClick={() => handleRunStrategy(1)}
                disabled={!strategies?.length}
              >
                Run Screening
              </Button>
            )}
          </Box>
        </Box>

        <Typography variant="body1" color="text.secondary">
          Create and run custom screening strategies to find stocks that match
          your criteria
        </Typography>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Market Overview */}
      <MarketOverview />

      {/* Main Content */}
      <Card sx={{ mt: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            aria-label="screener tabs"
          >
            <Tab
              label="Strategies"
              icon={<FilterList />}
              iconPosition="start"
              id="screener-tab-0"
              aria-controls="screener-tabpanel-0"
            />
            <Tab
              label="Results"
              icon={<Assessment />}
              iconPosition="start"
              id="screener-tab-1"
              aria-controls="screener-tabpanel-1"
            />
            <Tab
              label="Performance"
              icon={<TrendingUp />}
              iconPosition="start"
              id="screener-tab-2"
              aria-controls="screener-tabpanel-2"
            />
          </Tabs>
        </Box>

        <TabPanel value={activeTab} index={0}>
          <StrategyList
            strategies={strategies || []}
            isLoading={isLoading}
            onEdit={handleEditStrategy}
            onRun={handleRunStrategy}
            isRunning={isRunning}
          />
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          <ScreeningResults />
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h6" color="text.secondary">
              Performance Analytics
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Track the performance of your screening strategies over time
            </Typography>
            <Button variant="outlined" sx={{ mt: 2 }}>
              Coming Soon
            </Button>
          </Box>
        </TabPanel>
      </Card>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="create strategy"
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          background: 'linear-gradient(45deg, #00d4aa, #4fffdf)',
          '&:hover': {
            background: 'linear-gradient(45deg, #00a37c, #00d4aa)',
          },
        }}
        onClick={handleCreateStrategy}
      >
        <Add />
      </Fab>

      {/* Strategy Builder Dialog */}
      <Dialog
        open={strategyDialogOpen}
        onClose={handleCloseStrategyDialog}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            minHeight: '80vh',
          },
        }}
      >
        <DialogTitle>
          {selectedStrategy ? 'Edit Strategy' : 'Create New Strategy'}
        </DialogTitle>
        <DialogContent>
          <StrategyBuilder
            strategy={selectedStrategy}
            onSave={handleCloseStrategyDialog}
            onCancel={handleCloseStrategyDialog}
          />
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default Screener;
