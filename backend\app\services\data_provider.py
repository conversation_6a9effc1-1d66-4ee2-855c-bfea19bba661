"""
Data provider service for fetching market data from multiple sources
"""

import asyncio
import aiohttp
import yfinance as yf
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import json
import pandas as pd

from app.core.config import settings, derived_settings
from app.core.database import get_influxdb_write_api, <PERSON><PERSON><PERSON><PERSON><PERSON>, TimeSeriesManager
from app.utils.indicators import TechnicalIndicators

logger = logging.getLogger(__name__)

class DataProvider:
    """Main data provider service that aggregates multiple data sources"""
    
    def __init__(self):
        self.is_running = False
        self.session: Optional[aiohttp.ClientSession] = None
        self.data_sources = {}
        self.rate_limiters = {}

        # Initialize data sources
        self._init_data_sources()
    
    def _init_data_sources(self):
        """Initialize data source clients"""
        configs = derived_settings.get_data_provider_configs()

        # Yahoo Finance (free, no API key needed) - primary source
        self.data_sources["yahoo"] = {
            "rate_limit": configs["yahoo_finance"]["rate_limit"]
        }

        # Optional: Alpha Vantage (if API key provided)
        if configs["alpha_vantage"]["api_key"]:
            try:
                # Import only if needed to avoid dependency issues
                from alpha_vantage.timeseries import TimeSeries
                from alpha_vantage.techindicators import TechIndicators

                self.data_sources["alpha_vantage"] = {
                    "client": TimeSeries(key=configs["alpha_vantage"]["api_key"], output_format='pandas'),
                    "tech_client": TechIndicators(key=configs["alpha_vantage"]["api_key"], output_format='pandas'),
                    "rate_limit": configs["alpha_vantage"]["rate_limit"]
                }
            except ImportError:
                logger.warning("Alpha Vantage library not available")

        # Optional: Polygon.io (if API key provided)
        if configs["polygon"]["api_key"]:
            try:
                from polygon import RESTClient
                self.data_sources["polygon"] = {
                    "client": RESTClient(configs["polygon"]["api_key"]),
                    "rate_limit": configs["polygon"]["rate_limit"]
                }
            except ImportError:
                logger.warning("Polygon library not available")
    
    async def start(self):
        """Start the data provider service"""
        if self.is_running:
            return
        
        self.session = aiohttp.ClientSession()
        self.is_running = True
        
        # Start background tasks
        asyncio.create_task(self._periodic_data_update())
        
        logger.info("Data provider service started")
    
    async def stop(self):
        """Stop the data provider service"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self.session:
            await self.session.close()
        
        logger.info("Data provider service stopped")
    
    async def get_stock_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get real-time stock quote"""
        cache_key = f"quote:{symbol}"
        
        # Check cache first
        cached_data = await CacheManager.get(cache_key)
        if cached_data:
            return json.loads(cached_data)
        
        # Try different data sources
        quote_data = None
        
        # Try Yahoo Finance first (free and reliable)
        try:
            quote_data = await self._get_yahoo_quote(symbol)
        except Exception as e:
            logger.warning(f"Yahoo Finance failed for {symbol}: {e}")
        
        # Fallback to Alpha Vantage
        if not quote_data and "alpha_vantage" in self.data_sources:
            try:
                quote_data = await self._get_alpha_vantage_quote(symbol)
            except Exception as e:
                logger.warning(f"Alpha Vantage failed for {symbol}: {e}")
        
        # Fallback to Polygon
        if not quote_data and "polygon" in self.data_sources:
            try:
                quote_data = await self._get_polygon_quote(symbol)
            except Exception as e:
                logger.warning(f"Polygon failed for {symbol}: {e}")
        
        if quote_data:
            # Cache for 30 seconds
            await CacheManager.set(cache_key, json.dumps(quote_data), 30)
        
        return quote_data
    
    async def _get_yahoo_quote(self, symbol: str) -> Dict[str, Any]:
        """Get quote from Yahoo Finance"""
        ticker = yf.Ticker(symbol)
        info = ticker.info
        
        if not info or 'regularMarketPrice' not in info:
            raise ValueError(f"No data found for {symbol}")
        
        return {
            "symbol": symbol,
            "price": info.get('regularMarketPrice'),
            "previous_close": info.get('previousClose'),
            "open": info.get('regularMarketOpen'),
            "high": info.get('dayHigh'),
            "low": info.get('dayLow'),
            "volume": info.get('regularMarketVolume'),
            "market_cap": info.get('marketCap'),
            "pe_ratio": info.get('trailingPE'),
            "dividend_yield": info.get('dividendYield'),
            "timestamp": datetime.utcnow().isoformat(),
            "source": "yahoo"
        }
    
    async def _get_alpha_vantage_quote(self, symbol: str) -> Dict[str, Any]:
        """Get quote from Alpha Vantage"""
        if "alpha_vantage" not in self.data_sources:
            raise ValueError("Alpha Vantage not configured")
        
        client = self.data_sources["alpha_vantage"]["client"]
        data, meta_data = client.get_quote_endpoint(symbol)
        
        if data.empty:
            raise ValueError(f"No data found for {symbol}")
        
        quote = data.iloc[0]
        return {
            "symbol": symbol,
            "price": float(quote['05. price']),
            "previous_close": float(quote['08. previous close']),
            "open": float(quote['02. open']),
            "high": float(quote['03. high']),
            "low": float(quote['04. low']),
            "volume": int(quote['06. volume']),
            "timestamp": datetime.utcnow().isoformat(),
            "source": "alpha_vantage"
        }
    
    async def _get_polygon_quote(self, symbol: str) -> Dict[str, Any]:
        """Get quote from Polygon.io"""
        if "polygon" not in self.data_sources:
            raise ValueError("Polygon not configured")
        
        client = self.data_sources["polygon"]["client"]
        quote = client.get_last_quote(symbol)
        
        if not quote:
            raise ValueError(f"No data found for {symbol}")
        
        return {
            "symbol": symbol,
            "price": quote.last.price,
            "bid_price": quote.last.bid,
            "ask_price": quote.last.ask,
            "bid_size": quote.last.bid_size,
            "ask_size": quote.last.ask_size,
            "timestamp": datetime.utcnow().isoformat(),
            "source": "polygon"
        }
    
    async def get_historical_data(
        self,
        symbol: str,
        period: str = "1y",
        interval: str = "1d"
    ) -> Optional[List[Dict[str, Any]]]:
        """Get historical market data"""
        cache_key = f"historical:{symbol}:{period}:{interval}"
        
        # Check cache first (cache for 1 hour)
        cached_data = await CacheManager.get(cache_key)
        if cached_data:
            return json.loads(cached_data)
        
        try:
            # Use Yahoo Finance for historical data (most reliable and free)
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period, interval=interval)
            
            if hist.empty:
                return None
            
            # Convert to list of dictionaries
            data = []
            for index, row in hist.iterrows():
                data.append({
                    "timestamp": index.isoformat(),
                    "open": float(row['Open']),
                    "high": float(row['High']),
                    "low": float(row['Low']),
                    "close": float(row['Close']),
                    "volume": int(row['Volume']),
                    "symbol": symbol
                })
            
            # Cache for 1 hour
            await CacheManager.set(cache_key, json.dumps(data), 3600)
            
            # Store in InfluxDB for long-term storage
            await self._store_historical_data(symbol, data)
            
            return data
            
        except Exception as e:
            logger.error(f"Error fetching historical data for {symbol}: {e}")
            return None
    
    async def get_technical_indicators(
        self,
        symbol: str,
        indicators: List[str]
    ) -> Dict[str, Any]:
        """Get technical indicators for a symbol"""
        cache_key = f"indicators:{symbol}:{':'.join(sorted(indicators))}"
        
        # Check cache first
        cached_data = await CacheManager.get(cache_key)
        if cached_data:
            return json.loads(cached_data)
        
        try:
            # Get historical data first
            historical_data = await self.get_historical_data(symbol, period="6mo", interval="1d")
            if not historical_data:
                return {}
            
            # Calculate indicators
            tech_indicators = TechnicalIndicators()
            results = {}
            
            # Extract price data
            closes = [float(d['close']) for d in historical_data]
            highs = [float(d['high']) for d in historical_data]
            lows = [float(d['low']) for d in historical_data]
            volumes = [int(d['volume']) for d in historical_data]
            
            for indicator in indicators:
                if indicator == "rsi":
                    results["rsi"] = tech_indicators.calculate_rsi(closes)
                elif indicator == "macd":
                    macd_data = tech_indicators.calculate_macd(closes)
                    results["macd"] = macd_data
                elif indicator == "bollinger_bands":
                    bb_data = tech_indicators.calculate_bollinger_bands(closes)
                    results["bollinger_bands"] = bb_data
                elif indicator == "sma_20":
                    results["sma_20"] = tech_indicators.calculate_sma(closes, 20)
                elif indicator == "sma_50":
                    results["sma_50"] = tech_indicators.calculate_sma(closes, 50)
                elif indicator == "sma_200":
                    results["sma_200"] = tech_indicators.calculate_sma(closes, 200)
                elif indicator == "ema_12":
                    results["ema_12"] = tech_indicators.calculate_ema(closes, 12)
                elif indicator == "ema_26":
                    results["ema_26"] = tech_indicators.calculate_ema(closes, 26)
            
            # Cache for 5 minutes
            await CacheManager.set(cache_key, json.dumps(results), 300)
            
            return results
            
        except Exception as e:
            logger.error(f"Error calculating technical indicators for {symbol}: {e}")
            return {}
    
    async def get_fundamental_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get fundamental data for a symbol"""
        cache_key = f"fundamentals:{symbol}"
        
        # Check cache first (cache for 1 day)
        cached_data = await CacheManager.get(cache_key)
        if cached_data:
            return json.loads(cached_data)
        
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            if not info:
                return None
            
            fundamental_data = {
                "symbol": symbol,
                "market_cap": info.get('marketCap'),
                "pe_ratio": info.get('trailingPE'),
                "forward_pe": info.get('forwardPE'),
                "pb_ratio": info.get('priceToBook'),
                "ps_ratio": info.get('priceToSalesTrailing12Months'),
                "peg_ratio": info.get('pegRatio'),
                "dividend_yield": info.get('dividendYield'),
                "eps": info.get('trailingEps'),
                "revenue": info.get('totalRevenue'),
                "profit_margin": info.get('profitMargins'),
                "operating_margin": info.get('operatingMargins'),
                "roe": info.get('returnOnEquity'),
                "roa": info.get('returnOnAssets'),
                "debt_to_equity": info.get('debtToEquity'),
                "current_ratio": info.get('currentRatio'),
                "quick_ratio": info.get('quickRatio'),
                "sector": info.get('sector'),
                "industry": info.get('industry'),
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Cache for 1 day
            await CacheManager.set(cache_key, json.dumps(fundamental_data), 86400)
            
            return fundamental_data
            
        except Exception as e:
            logger.error(f"Error fetching fundamental data for {symbol}: {e}")
            return None
    
    async def _store_historical_data(self, symbol: str, data: List[Dict[str, Any]]):
        """Store historical data in InfluxDB"""
        try:
            for record in data:
                await TimeSeriesManager.write_market_data(
                    symbol=symbol,
                    data={
                        "open": record["open"],
                        "high": record["high"],
                        "low": record["low"],
                        "close": record["close"],
                        "volume": record["volume"]
                    },
                    timestamp=record["timestamp"]
                )
        except Exception as e:
            logger.error(f"Error storing historical data for {symbol}: {e}")
    
    async def _periodic_data_update(self):
        """Periodic task to update market data and stream to WebSocket clients"""
        while self.is_running:
            try:
                # Get symbols that have active WebSocket subscriptions
                from app.services.websocket_manager import connection_manager

                if hasattr(connection_manager, 'symbol_subscriptions'):
                    active_symbols = list(connection_manager.symbol_subscriptions.keys())

                    if active_symbols:
                        # Update quotes for active symbols
                        await self._update_active_symbols(active_symbols)

                await asyncio.sleep(settings.DATA_UPDATE_INTERVAL)
            except Exception as e:
                logger.error(f"Error in periodic data update: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying

    async def _update_active_symbols(self, symbols: List[str]):
        """Update quotes for active symbols and broadcast to WebSocket clients"""
        from app.services.websocket_manager import connection_manager

        for symbol in symbols:
            try:
                # Get fresh quote data (bypass cache for real-time updates)
                quote_data = await self._get_yahoo_quote(symbol)

                if quote_data:
                    # Store in cache
                    cache_key = f"quote:{symbol}"
                    await CacheManager.set(cache_key, json.dumps(quote_data), 30)

                    # Broadcast to WebSocket subscribers
                    await connection_manager.broadcast_quote_update(symbol, quote_data)

                    # Store in InfluxDB for historical tracking
                    await self._store_real_time_data(symbol, quote_data)

            except Exception as e:
                logger.error(f"Error updating symbol {symbol}: {e}")

    async def _store_real_time_data(self, symbol: str, quote_data: Dict[str, Any]):
        """Store real-time quote data in InfluxDB"""
        try:
            await TimeSeriesManager.write_market_data(
                symbol=symbol,
                data={
                    "price": quote_data.get("price", 0),
                    "volume": quote_data.get("volume", 0),
                    "change": quote_data.get("change", 0),
                    "change_percent": quote_data.get("change_percent", 0),
                    "bid": quote_data.get("bid", 0),
                    "ask": quote_data.get("ask", 0),
                    "bid_size": quote_data.get("bid_size", 0),
                    "ask_size": quote_data.get("ask_size", 0)
                },
                timestamp=quote_data.get("timestamp", datetime.utcnow().isoformat())
            )
        except Exception as e:
            logger.error(f"Error storing real-time data for {symbol}: {e}")
    
    async def get_market_status(self) -> Dict[str, Any]:
        """Get market status information"""
        try:
            # Simple market hours check (US markets)
            now = datetime.now()
            market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
            market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)
            
            is_market_hours = (
                now.weekday() < 5 and  # Monday to Friday
                market_open <= now <= market_close
            )
            
            return {
                "is_open": is_market_hours,
                "next_open": market_open.isoformat() if not is_market_hours else None,
                "next_close": market_close.isoformat() if is_market_hours else None,
                "timezone": "US/Eastern"
            }
        except Exception as e:
            logger.error(f"Error getting market status: {e}")
            return {"is_open": False, "error": str(e)}

    async def get_sp500_symbols(self) -> List[str]:
        """Get S&P 500 symbols"""
        try:
            cache_key = "sp500_symbols"
            cached_data = await CacheManager.get(cache_key)
            if cached_data:
                return cached_data

            # Fallback to a subset of common S&P 500 symbols
            symbols = [
                "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "BRK-B",
                "UNH", "JNJ", "JPM", "V", "PG", "HD", "MA", "BAC", "ABBV", "PFE",
                "KO", "AVGO", "PEP", "TMO", "COST", "WMT", "DIS", "ABT", "DHR",
                "VZ", "ADBE", "NFLX", "CRM", "XOM", "NKE", "CMCSA", "T", "INTC",
                "CVX", "LLY", "ACN", "MRK", "ORCL", "WFC", "MDT", "UPS", "BMY",
                "QCOM", "TXN", "LIN", "NEE", "PM", "RTX", "LOW", "HON", "SPGI",
                "IBM", "C", "SBUX", "CAT", "DE", "INTU", "GS", "AXP", "BLK"
            ]

            # Cache for 24 hours
            await CacheManager.set(cache_key, json.dumps(symbols), 86400)

            return symbols

        except Exception as e:
            logger.error(f"Error getting S&P 500 symbols: {e}")
            return symbols  # Return the fallback list

    async def get_nasdaq100_symbols(self) -> List[str]:
        """Get NASDAQ 100 symbols"""
        try:
            cache_key = "nasdaq100_symbols"
            cached_data = await CacheManager.get(cache_key)
            if cached_data:
                return json.loads(cached_data)

            # Fallback to common NASDAQ 100 symbols
            symbols = [
                "AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "TSLA", "META", "NVDA",
                "AVGO", "PEP", "COST", "ADBE", "NFLX", "CRM", "INTC", "CMCSA",
                "TXN", "QCOM", "TMUS", "HON", "AMGN", "SBUX", "INTU", "AMD",
                "ISRG", "BKNG", "MDLZ", "GILD", "ADP", "VRTX", "ADI", "LRCX",
                "PYPL", "REGN", "ATVI", "FISV", "CSX", "MRNA", "KLAC", "ORLY",
                "MCHP", "FTNT", "DXCM", "ASML", "MNST", "WDAY", "NXPI", "SNPS",
                "CDNS", "MELI", "CTAS", "CHTR", "MAR", "PAYX", "ABNB", "ROST"
            ]

            # Cache for 24 hours
            await CacheManager.set(cache_key, json.dumps(symbols), 86400)

            return symbols

        except Exception as e:
            logger.error(f"Error getting NASDAQ 100 symbols: {e}")
            return symbols  # Return the fallback list
