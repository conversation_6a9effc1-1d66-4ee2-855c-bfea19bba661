"""
WebSocket API endpoints for real-time data streaming
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, status, Query
from fastapi.security import HTT<PERSON><PERSON>earer
from typing import Optional
import logging
import jwt

from app.core.config import settings
from app.core.security import get_current_user
from app.models.user import User
from app.services.websocket_manager import connection_manager
from app.services.analytics_service import analytics_service, EventType
from app.utils.rate_limiter import api_rate_limiter

router = APIRouter()
logger = logging.getLogger(__name__)

security = HTTPBearer()


async def get_user_from_websocket_token(token: str) -> User:
    """Get user from WebSocket token"""
    try:
        # Decode JWT token
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        user_id: int = payload.get("sub")
        
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        
        # Get user from database - we'll need to implement token-based auth
        # For now, let's create a simple token validation
        from app.core.database import get_db
        from sqlalchemy import select

        async with get_db() as db:
            result = await db.execute(select(User).where(User.id == user_id))
            user = result.scalar_one_or_none()

            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found"
                )
        return user
        
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )


@router.websocket("/stream")
async def websocket_endpoint(
    websocket: WebSocket,
    token: str = Query(..., description="JWT authentication token")
):
    """WebSocket endpoint for real-time data streaming"""
    try:
        # Authenticate user
        user = await get_user_from_websocket_token(token)
        
        # Check if user is active
        if not user.is_active:
            await websocket.close(code=4001, reason="User account is inactive")
            return
        
        # Connect to WebSocket manager
        await connection_manager.connect(websocket, user.id, user.subscription_tier)
        
        try:
            while True:
                # Receive message from client
                data = await websocket.receive_text()
                
                # Handle the message
                await connection_manager.handle_message(user.id, data)
                
        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected for user {user.id}")
        except Exception as e:
            logger.error(f"WebSocket error for user {user.id}: {e}")
        finally:
            # Clean up connection
            await connection_manager.disconnect(user.id)
            
    except HTTPException as e:
        # Authentication failed
        await websocket.close(code=4001, reason=e.detail)
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
        await websocket.close(code=4000, reason="Internal server error")


@router.get("/connections")
async def get_websocket_connections(
    current_user: User = Depends(get_current_user)
):
    """Get WebSocket connection statistics (Admin only)"""
    # Check if user is admin (you might want to implement proper admin check)
    if current_user.subscription_tier != "enterprise":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    
    # Apply rate limiting
    await api_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier,
        endpoint="websocket_stats"
    )
    
    stats = connection_manager.get_connection_stats()
    return {
        "websocket_stats": stats,
        "timestamp": analytics_service._last_flush
    }


@router.post("/broadcast")
async def broadcast_message(
    message_type: str,
    message_data: dict,
    target_users: Optional[list] = None,
    current_user: User = Depends(get_current_user)
):
    """Broadcast message to WebSocket clients (Admin only)"""
    # Check if user is admin
    if current_user.subscription_tier != "enterprise":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    
    # Apply rate limiting
    await api_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier,
        endpoint="websocket_broadcast"
    )
    
    # Track broadcast event
    await analytics_service.track_event(
        event_type=EventType.FEATURE_USAGE,
        user_id=current_user.id,
        data={
            "feature": "websocket_broadcast",
            "message_type": message_type,
            "target_users": len(target_users) if target_users else "all"
        },
        subscription_tier=current_user.subscription_tier
    )
    
    # Implement broadcast logic here
    # This would depend on the specific message type and targets
    
    return {
        "status": "success",
        "message": f"Broadcast sent to {len(target_users) if target_users else 'all'} users",
        "message_type": message_type
    }


@router.get("/health")
async def websocket_health():
    """WebSocket service health check"""
    stats = connection_manager.get_connection_stats()
    
    return {
        "status": "healthy",
        "active_connections": stats["active_connections"],
        "total_subscriptions": stats["total_subscriptions"],
        "unique_symbols": stats["unique_symbols"],
        "service": "websocket_manager"
    }
