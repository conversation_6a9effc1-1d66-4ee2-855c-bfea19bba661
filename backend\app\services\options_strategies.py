"""
Multi-leg options strategies with automated execution and risk management
"""

import asyncio
import logging
import numpy as np
from typing import Dict, List, Optional, Any, <PERSON><PERSON>
from datetime import datetime, timed<PERSON>ta
from sqlalchemy import select, and_
from abc import ABC, abstractmethod
from decimal import Decimal

from app.core.config import settings
from app.core.database import get_db
from app.models.options import OptionContract, OptionTrade, OptionPosition, OptionStrategy, StrategyType, OptionType
from app.models.stock import Stock
from app.models.user import BrokerAccount
from app.services.data_provider import DataProvider
from app.services.broker_integration import BrokerManager

logger = logging.getLogger(__name__)

class OptionsStrategyBase(ABC):
    """Base class for options strategies"""
    
    def __init__(self, name: str, strategy_type: StrategyType):
        self.name = name
        self.strategy_type = strategy_type
        self.data_provider = DataProvider()
        self.broker_manager = BrokerManager()
        
    @abstractmethod
    async def validate_strategy(self, legs: List[Dict[str, Any]], underlying_price: float) -> Dict[str, Any]:
        """Validate strategy configuration"""
        pass
    
    @abstractmethod
    async def calculate_max_profit(self, legs: List[Dict[str, Any]]) -> Optional[float]:
        """Calculate maximum profit potential"""
        pass
    
    @abstractmethod
    async def calculate_max_loss(self, legs: List[Dict[str, Any]]) -> Optional[float]:
        """Calculate maximum loss potential"""
        pass
    
    @abstractmethod
    async def calculate_breakeven_points(self, legs: List[Dict[str, Any]]) -> List[float]:
        """Calculate breakeven points"""
        pass
    
    @abstractmethod
    async def get_optimal_strikes(self, underlying_price: float, expiration_date: datetime, 
                                 volatility: float) -> List[Dict[str, Any]]:
        """Get optimal strike prices for the strategy"""
        pass

class IronCondorStrategy(OptionsStrategyBase):
    """Iron Condor options strategy implementation"""
    
    def __init__(self):
        super().__init__("Iron Condor", StrategyType.IRON_CONDOR)
        
    async def validate_strategy(self, legs: List[Dict[str, Any]], underlying_price: float) -> Dict[str, Any]:
        """Validate iron condor configuration"""
        try:
            if len(legs) != 4:
                return {"valid": False, "error": "Iron Condor requires exactly 4 legs"}
            
            # Sort legs by strike price
            sorted_legs = sorted(legs, key=lambda x: x["strike_price"])
            
            # Validate structure: short put, long put, long call, short call
            expected_structure = [
                {"option_type": "put", "action": "sell"},    # Short put (lowest strike)
                {"option_type": "put", "action": "buy"},     # Long put
                {"option_type": "call", "action": "buy"},    # Long call
                {"option_type": "call", "action": "sell"}    # Short call (highest strike)
            ]
            
            for i, (leg, expected) in enumerate(zip(sorted_legs, expected_structure)):
                if (leg["option_type"] != expected["option_type"] or 
                    leg["action"] != expected["action"]):
                    return {"valid": False, "error": f"Invalid leg {i+1} structure"}
            
            # Validate strike spacing
            put_spread_width = sorted_legs[1]["strike_price"] - sorted_legs[0]["strike_price"]
            call_spread_width = sorted_legs[3]["strike_price"] - sorted_legs[2]["strike_price"]
            
            if put_spread_width != call_spread_width:
                return {"valid": False, "error": "Put and call spreads must have equal width"}
            
            # Validate underlying price is between short strikes
            short_put_strike = sorted_legs[0]["strike_price"]
            short_call_strike = sorted_legs[3]["strike_price"]
            
            if not (short_put_strike < underlying_price < short_call_strike):
                return {"valid": False, "warning": "Underlying price should be between short strikes for optimal setup"}
            
            return {"valid": True, "structure": sorted_legs}
            
        except Exception as e:
            logger.error(f"Error validating iron condor: {e}")
            return {"valid": False, "error": str(e)}
    
    async def calculate_max_profit(self, legs: List[Dict[str, Any]]) -> Optional[float]:
        """Calculate maximum profit for iron condor"""
        try:
            # Max profit = Net credit received
            net_credit = 0.0
            for leg in legs:
                premium = leg.get("premium", 0.0)
                if leg["action"] == "sell":
                    net_credit += premium
                else:
                    net_credit -= premium
            
            return max(0, net_credit)
            
        except Exception as e:
            logger.error(f"Error calculating iron condor max profit: {e}")
            return None
    
    async def calculate_max_loss(self, legs: List[Dict[str, Any]]) -> Optional[float]:
        """Calculate maximum loss for iron condor"""
        try:
            sorted_legs = sorted(legs, key=lambda x: x["strike_price"])
            
            # Max loss = Strike width - Net credit
            strike_width = sorted_legs[1]["strike_price"] - sorted_legs[0]["strike_price"]
            net_credit = await self.calculate_max_profit(legs)
            
            return strike_width - (net_credit or 0)
            
        except Exception as e:
            logger.error(f"Error calculating iron condor max loss: {e}")
            return None
    
    async def calculate_breakeven_points(self, legs: List[Dict[str, Any]]) -> List[float]:
        """Calculate breakeven points for iron condor"""
        try:
            sorted_legs = sorted(legs, key=lambda x: x["strike_price"])
            net_credit = await self.calculate_max_profit(legs) or 0
            
            # Lower breakeven = Short put strike + Net credit
            # Upper breakeven = Short call strike - Net credit
            lower_breakeven = sorted_legs[0]["strike_price"] + net_credit
            upper_breakeven = sorted_legs[3]["strike_price"] - net_credit
            
            return [lower_breakeven, upper_breakeven]
            
        except Exception as e:
            logger.error(f"Error calculating iron condor breakevens: {e}")
            return []
    
    async def get_optimal_strikes(self, underlying_price: float, expiration_date: datetime, 
                                 volatility: float) -> List[Dict[str, Any]]:
        """Get optimal strikes for iron condor"""
        try:
            # Calculate strike spacing based on volatility and time to expiration
            days_to_expiration = (expiration_date.date() - datetime.now().date()).days
            time_factor = max(0.1, days_to_expiration / 365.0)
            
            # Strike spacing = underlying_price * volatility * sqrt(time_factor)
            strike_spacing = underlying_price * volatility * np.sqrt(time_factor) * 0.5
            
            # Round to nearest $5 for liquid strikes
            strike_spacing = max(5, round(strike_spacing / 5) * 5)
            
            return [
                {
                    "option_type": "put",
                    "action": "sell",
                    "strike_price": underlying_price - strike_spacing,
                    "description": "Short Put"
                },
                {
                    "option_type": "put", 
                    "action": "buy",
                    "strike_price": underlying_price - (strike_spacing * 2),
                    "description": "Long Put"
                },
                {
                    "option_type": "call",
                    "action": "buy", 
                    "strike_price": underlying_price + strike_spacing,
                    "description": "Long Call"
                },
                {
                    "option_type": "call",
                    "action": "sell",
                    "strike_price": underlying_price + (strike_spacing * 2),
                    "description": "Short Call"
                }
            ]
            
        except Exception as e:
            logger.error(f"Error getting optimal iron condor strikes: {e}")
            return []

class StraddleStrategy(OptionsStrategyBase):
    """Straddle options strategy implementation"""
    
    def __init__(self):
        super().__init__("Straddle", StrategyType.STRADDLE)
        
    async def validate_strategy(self, legs: List[Dict[str, Any]], underlying_price: float) -> Dict[str, Any]:
        """Validate straddle configuration"""
        try:
            if len(legs) != 2:
                return {"valid": False, "error": "Straddle requires exactly 2 legs"}
            
            # Validate structure: call and put at same strike
            call_leg = next((leg for leg in legs if leg["option_type"] == "call"), None)
            put_leg = next((leg for leg in legs if leg["option_type"] == "put"), None)
            
            if not call_leg or not put_leg:
                return {"valid": False, "error": "Straddle requires one call and one put"}
            
            if call_leg["strike_price"] != put_leg["strike_price"]:
                return {"valid": False, "error": "Call and put must have same strike price"}
            
            if call_leg["action"] != put_leg["action"]:
                return {"valid": False, "error": "Call and put must have same action (both buy or both sell)"}
            
            return {"valid": True, "structure": legs}
            
        except Exception as e:
            logger.error(f"Error validating straddle: {e}")
            return {"valid": False, "error": str(e)}
    
    async def calculate_max_profit(self, legs: List[Dict[str, Any]]) -> Optional[float]:
        """Calculate maximum profit for straddle"""
        try:
            # Long straddle: Unlimited profit potential
            # Short straddle: Limited to net credit received
            action = legs[0]["action"]  # Both legs have same action
            
            if action == "buy":
                return float('inf')  # Unlimited profit potential
            else:
                # Net credit received
                return sum(leg.get("premium", 0.0) for leg in legs)
                
        except Exception as e:
            logger.error(f"Error calculating straddle max profit: {e}")
            return None
    
    async def calculate_max_loss(self, legs: List[Dict[str, Any]]) -> Optional[float]:
        """Calculate maximum loss for straddle"""
        try:
            action = legs[0]["action"]
            
            if action == "buy":
                # Net debit paid
                return sum(leg.get("premium", 0.0) for leg in legs)
            else:
                return float('inf')  # Unlimited loss potential for short straddle
                
        except Exception as e:
            logger.error(f"Error calculating straddle max loss: {e}")
            return None
    
    async def calculate_breakeven_points(self, legs: List[Dict[str, Any]]) -> List[float]:
        """Calculate breakeven points for straddle"""
        try:
            strike_price = legs[0]["strike_price"]
            total_premium = sum(leg.get("premium", 0.0) for leg in legs)
            
            # Upper breakeven = Strike + Total premium
            # Lower breakeven = Strike - Total premium
            return [strike_price - total_premium, strike_price + total_premium]
            
        except Exception as e:
            logger.error(f"Error calculating straddle breakevens: {e}")
            return []
    
    async def get_optimal_strikes(self, underlying_price: float, expiration_date: datetime, 
                                 volatility: float) -> List[Dict[str, Any]]:
        """Get optimal strikes for straddle"""
        try:
            # Straddle typically uses ATM strikes
            atm_strike = round(underlying_price / 5) * 5  # Round to nearest $5
            
            return [
                {
                    "option_type": "call",
                    "action": "buy",
                    "strike_price": atm_strike,
                    "description": "Long Call"
                },
                {
                    "option_type": "put",
                    "action": "buy", 
                    "strike_price": atm_strike,
                    "description": "Long Put"
                }
            ]
            
        except Exception as e:
            logger.error(f"Error getting optimal straddle strikes: {e}")
            return []


class OptionsStrategyManager:
    """Manager for all options strategies"""

    def __init__(self):
        self.strategies = {
            StrategyType.IRON_CONDOR: IronCondorStrategy(),
            StrategyType.STRADDLE: StraddleStrategy(),
            # Add more strategies as they're implemented
        }

    def get_strategy(self, strategy_type: StrategyType) -> Optional[OptionsStrategyBase]:
        """Get strategy instance by type"""
        return self.strategies.get(strategy_type)

    async def create_options_strategy(
        self,
        user_id: int,
        broker_account_id: int,
        underlying_symbol: str,
        strategy_type: StrategyType,
        legs: List[Dict[str, Any]],
        quantity: int = 1,
        expiration_date: Optional[datetime] = None
    ) -> Optional[Dict[str, Any]]:
        """Create and validate an options strategy"""
        try:
            async with get_db() as db:
                # Get underlying stock
                result = await db.execute(
                    select(Stock).where(Stock.symbol == underlying_symbol)
                )
                stock = result.scalar_one_or_none()

                if not stock:
                    return {"error": f"Stock {underlying_symbol} not found"}

                # Get strategy implementation
                strategy = self.get_strategy(strategy_type)
                if not strategy:
                    return {"error": f"Strategy type {strategy_type.value} not supported"}

                # Validate strategy
                validation = await strategy.validate_strategy(legs, stock.current_price or 0)
                if not validation.get("valid"):
                    return {"error": validation.get("error", "Invalid strategy")}

                # Calculate risk metrics
                max_profit = await strategy.calculate_max_profit(legs)
                max_loss = await strategy.calculate_max_loss(legs)
                breakeven_points = await strategy.calculate_breakeven_points(legs)

                # Calculate net debit/credit
                net_cost = 0.0
                for leg in legs:
                    premium = leg.get("premium", 0.0)
                    if leg["action"] == "buy":
                        net_cost += premium
                    else:
                        net_cost -= premium

                # Create strategy record
                option_strategy = OptionStrategy(
                    user_id=user_id,
                    broker_account_id=broker_account_id,
                    underlying_stock_id=stock.id,
                    name=f"{strategy.name} - {underlying_symbol}",
                    strategy_type=strategy_type,
                    legs=legs,
                    quantity=quantity,
                    max_profit=Decimal(str(max_profit)) if max_profit and max_profit != float('inf') else None,
                    max_loss=Decimal(str(max_loss)) if max_loss and max_loss != float('inf') else None,
                    break_even_points=breakeven_points,
                    total_cost=Decimal(str(net_cost * quantity)),
                    expires_at=expiration_date,
                    status="pending"
                )

                db.add(option_strategy)
                await db.commit()
                await db.refresh(option_strategy)

                return {
                    "strategy_id": option_strategy.id,
                    "strategy": option_strategy.to_dict(),
                    "validation": validation,
                    "risk_metrics": {
                        "max_profit": max_profit,
                        "max_loss": max_loss,
                        "breakeven_points": breakeven_points,
                        "net_cost": net_cost * quantity,
                        "risk_reward_ratio": (max_profit / max_loss) if max_profit and max_loss and max_loss > 0 else None
                    }
                }

        except Exception as e:
            logger.error(f"Error creating options strategy: {e}")
            return {"error": str(e)}

    async def execute_options_strategy(
        self,
        strategy_id: int,
        is_paper_trade: bool = True
    ) -> Dict[str, Any]:
        """Execute an options strategy by submitting all leg orders"""
        try:
            async with get_db() as db:
                # Get strategy
                result = await db.execute(
                    select(OptionStrategy).where(OptionStrategy.id == strategy_id)
                )
                strategy = result.scalar_one_or_none()

                if not strategy:
                    return {"error": "Strategy not found"}

                if strategy.status != "pending":
                    return {"error": f"Strategy is not pending (current status: {strategy.status})"}

                # Execute each leg
                executed_trades = []
                total_cost = 0.0

                for leg_config in strategy.legs:
                    # Find option contract
                    result = await db.execute(
                        select(OptionContract).where(
                            and_(
                                OptionContract.underlying_stock_id == strategy.underlying_stock_id,
                                OptionContract.option_type == OptionType(leg_config["option_type"]),
                                OptionContract.strike_price == Decimal(str(leg_config["strike_price"])),
                                OptionContract.expiration_date >= datetime.utcnow()
                            )
                        ).limit(1)
                    )
                    option_contract = result.scalar_one_or_none()

                    if not option_contract:
                        return {"error": f"Option contract not found for leg: {leg_config}"}

                    # Create option trade
                    option_trade = OptionTrade(
                        user_id=strategy.user_id,
                        option_contract_id=option_contract.id,
                        broker_account_id=strategy.broker_account_id,
                        option_strategy_id=strategy.id,
                        order_type="market",
                        order_side=f"{leg_config['action']}_to_open",
                        quantity=leg_config.get("quantity", 1) * strategy.quantity,
                        price=Decimal(str(leg_config.get("premium", 0.0))),
                        is_paper_trade=is_paper_trade,
                        status="pending"
                    )

                    db.add(option_trade)
                    executed_trades.append(option_trade)

                    # Calculate cost
                    premium = float(leg_config.get("premium", 0.0))
                    quantity = leg_config.get("quantity", 1) * strategy.quantity

                    if leg_config["action"] == "buy":
                        total_cost += premium * quantity * 100  # Contract multiplier
                    else:
                        total_cost -= premium * quantity * 100

                # Update strategy status
                strategy.status = "active"
                strategy.opened_at = datetime.utcnow()
                strategy.total_cost = Decimal(str(total_cost))

                await db.commit()

                # Refresh all trades
                for trade in executed_trades:
                    await db.refresh(trade)

                return {
                    "success": True,
                    "strategy_id": strategy_id,
                    "executed_trades": [trade.to_dict() for trade in executed_trades],
                    "total_cost": total_cost,
                    "status": strategy.status
                }

        except Exception as e:
            logger.error(f"Error executing options strategy: {e}")
            return {"error": str(e)}

    async def get_strategy_pnl(self, strategy_id: int) -> Dict[str, Any]:
        """Calculate current P&L for an options strategy"""
        try:
            async with get_db() as db:
                # Get strategy with positions
                result = await db.execute(
                    select(OptionStrategy).where(OptionStrategy.id == strategy_id)
                )
                strategy = result.scalar_one_or_none()

                if not strategy:
                    return {"error": "Strategy not found"}

                # Get all positions for this strategy
                result = await db.execute(
                    select(OptionPosition).where(
                        OptionPosition.option_strategy_id == strategy_id
                    )
                )
                positions = result.scalars().all()

                total_current_value = 0.0
                total_unrealized_pnl = 0.0

                for position in positions:
                    if position.current_price and position.market_value:
                        total_current_value += float(position.market_value)
                        if position.unrealized_pnl:
                            total_unrealized_pnl += float(position.unrealized_pnl)

                # Update strategy current value
                strategy.current_value = Decimal(str(total_current_value))
                strategy.unrealized_pnl = Decimal(str(total_unrealized_pnl))

                await db.commit()

                return {
                    "strategy_id": strategy_id,
                    "total_cost": float(strategy.total_cost) if strategy.total_cost else 0.0,
                    "current_value": total_current_value,
                    "unrealized_pnl": total_unrealized_pnl,
                    "unrealized_pnl_percent": (total_unrealized_pnl / abs(float(strategy.total_cost))) * 100 if strategy.total_cost else 0.0,
                    "positions": [pos.to_dict() for pos in positions]
                }

        except Exception as e:
            logger.error(f"Error calculating strategy P&L: {e}")
            return {"error": str(e)}
