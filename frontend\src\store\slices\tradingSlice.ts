import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Trade {
  id: number;
  symbol: string;
  quantity: number;
  price: number;
  status: string;
  created_at: string;
}

interface Position {
  id: number;
  symbol: string;
  quantity: number;
  average_cost: number;
  current_price: number;
  unrealized_pnl: number;
}

interface TradingState {
  trades: Trade[];
  positions: Position[];
  selectedTrade: Trade | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: TradingState = {
  trades: [],
  positions: [],
  selectedTrade: null,
  isLoading: false,
  error: null,
};

const tradingSlice = createSlice({
  name: 'trading',
  initialState,
  reducers: {
    setTrades: (state, action: PayloadAction<Trade[]>) => {
      state.trades = action.payload;
    },
    setPositions: (state, action: PayloadAction<Position[]>) => {
      state.positions = action.payload;
    },
    setSelectedTrade: (state, action: PayloadAction<Trade | null>) => {
      state.selectedTrade = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setTrades,
  setPositions,
  setSelectedTrade,
  setLoading,
  setError,
} = tradingSlice.actions;
export default tradingSlice.reducer;
