"""
Subscription Management API endpoints
"""

from fastapi import APIRouter, HTTPException, Depends, status, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
from datetime import datetime

from app.core.database import get_db
from app.core.security import get_current_active_user
from app.models.user import User
from app.services.subscription_manager import SubscriptionManager, require_subscription_tier
from app.utils.rate_limiter import api_rate_limiter

router = APIRouter()

# Pydantic models
class SubscriptionTierResponse(BaseModel):
    """Subscription tier information"""
    name: str
    price: float
    features: Dict[str, Any]

class SubscriptionStatusResponse(BaseModel):
    """User subscription status"""
    user_id: int
    current_tier: str
    tier_info: Dict[str, Any]
    limits_status: Dict[str, Any]
    subscription_active: bool
    can_upgrade: bool

class UpgradeRequest(BaseModel):
    """Subscription upgrade request"""
    target_tier: str = Field(..., description="Target subscription tier")
    payment_method_id: Optional[str] = Field(None, description="Payment method ID")

class UsageStatsResponse(BaseModel):
    """Usage statistics response"""
    feature: str
    current_usage: int
    limit: Optional[int]
    remaining: Optional[int]
    usage_percentage: float
    within_limit: bool
    warning: bool
    critical: bool

@router.get("/tiers", response_model=Dict[str, SubscriptionTierResponse])
async def get_subscription_tiers():
    """Get all available subscription tiers"""
    return SubscriptionManager.get_all_tiers()

@router.get("/status", response_model=SubscriptionStatusResponse)
async def get_subscription_status(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user's subscription status"""
    # Apply rate limiting
    await api_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier,
        endpoint="subscription_status"
    )
    
    status = await SubscriptionManager.get_user_subscription_status(current_user)
    return status

@router.get("/usage/{feature}", response_model=UsageStatsResponse)
async def get_feature_usage(
    feature: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get usage statistics for a specific feature"""
    # Apply rate limiting
    await api_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier,
        endpoint="usage_stats"
    )
    
    # Get current usage (this would typically query the database)
    current_usage = 0
    if feature == "portfolios":
        current_usage = len(current_user.portfolios) if current_user.portfolios else 0
    elif feature == "strategies":
        current_usage = len(current_user.strategies) if current_user.strategies else 0
    elif feature == "api_calls_per_day":
        # Would query from rate limiting logs
        current_usage = 0
    
    usage_info = await SubscriptionManager.check_usage_limits(
        current_user, feature, current_usage
    )
    
    return {
        "feature": feature,
        **usage_info
    }

@router.get("/compare/{target_tier}")
async def compare_subscription_tiers(
    target_tier: str,
    current_user: User = Depends(get_current_active_user)
):
    """Compare current subscription with target tier"""
    # Apply rate limiting
    await api_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier,
        endpoint="tier_comparison"
    )
    
    if target_tier not in SubscriptionManager.SUBSCRIPTION_TIERS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid subscription tier: {target_tier}"
        )
    
    comparison = SubscriptionManager.compare_tiers(
        current_user.subscription_tier, target_tier
    )
    
    return comparison

@router.post("/upgrade")
async def upgrade_subscription(
    upgrade_request: UpgradeRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """Upgrade user's subscription tier"""
    # Apply rate limiting
    await api_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier,
        endpoint="subscription_upgrade"
    )
    
    target_tier = upgrade_request.target_tier
    
    # Validate target tier
    if target_tier not in SubscriptionManager.SUBSCRIPTION_TIERS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid subscription tier: {target_tier}"
        )
    
    # Check if it's actually an upgrade
    comparison = SubscriptionManager.compare_tiers(
        current_user.subscription_tier, target_tier
    )
    
    if not comparison["is_upgrade"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Target tier is not an upgrade from current tier"
        )
    
    # In a real implementation, you would:
    # 1. Process payment using payment_method_id
    # 2. Update user's subscription in database
    # 3. Send confirmation emails
    # 4. Update user limits
    
    # For now, simulate the upgrade
    async with get_db() as db:
        current_user.subscription_tier = target_tier
        
        # Update user limits based on new tier
        tier_info = SubscriptionManager.get_tier_info(target_tier)
        current_user.max_portfolios = tier_info["features"]["max_portfolios"]
        current_user.max_strategies = tier_info["features"]["max_strategies"]
        current_user.max_api_calls_per_day = tier_info["features"]["max_api_calls_per_day"]
        
        await db.commit()
    
    # Add background task for post-upgrade actions
    background_tasks.add_task(
        _post_upgrade_actions,
        current_user.id,
        current_user.subscription_tier,
        target_tier
    )
    
    return {
        "message": f"Successfully upgraded to {tier_info['name']} tier",
        "new_tier": target_tier,
        "new_limits": tier_info["features"],
        "effective_date": datetime.utcnow().isoformat()
    }

@router.get("/suggest-upgrade")
async def suggest_subscription_upgrade(
    feature: str,
    required_limit: int,
    current_user: User = Depends(get_current_active_user)
):
    """Suggest appropriate subscription upgrade based on requirements"""
    # Apply rate limiting
    await api_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier,
        endpoint="upgrade_suggestion"
    )
    
    suggested_tier = await SubscriptionManager.suggest_upgrade(
        current_user, feature, required_limit
    )
    
    if not suggested_tier:
        return {
            "suggestion": None,
            "message": "No upgrade needed or available for the requested feature limit"
        }
    
    tier_info = SubscriptionManager.get_tier_info(suggested_tier)
    comparison = SubscriptionManager.compare_tiers(
        current_user.subscription_tier, suggested_tier
    )
    
    return {
        "suggestion": suggested_tier,
        "tier_info": tier_info,
        "price_difference": comparison["price_difference"],
        "feature_improvements": comparison["feature_improvements"],
        "upgrade_url": f"/api/v1/subscription/upgrade"
    }

@router.get("/limits")
async def get_subscription_limits(
    current_user: User = Depends(get_current_active_user)
):
    """Get all subscription limits for current user"""
    # Apply rate limiting
    await api_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier,
        endpoint="subscription_limits"
    )
    
    tier_info = SubscriptionManager.get_tier_info(current_user.subscription_tier)
    
    return {
        "subscription_tier": current_user.subscription_tier,
        "limits": tier_info["features"],
        "tier_name": tier_info["name"],
        "monthly_price": tier_info["price"]
    }

# Premium feature endpoint example
@router.get("/premium-feature")
async def premium_feature_example(
    current_user: User = Depends(require_subscription_tier("premium"))
):
    """Example endpoint that requires premium subscription"""
    return {
        "message": "This is a premium feature!",
        "user_tier": current_user.subscription_tier,
        "access_granted": True
    }

# Enterprise feature endpoint example
@router.get("/enterprise-feature")
async def enterprise_feature_example(
    current_user: User = Depends(require_subscription_tier("enterprise"))
):
    """Example endpoint that requires enterprise subscription"""
    return {
        "message": "This is an enterprise feature!",
        "user_tier": current_user.subscription_tier,
        "access_granted": True,
        "advanced_features": True
    }

async def _post_upgrade_actions(user_id: int, old_tier: str, new_tier: str):
    """Background task for post-upgrade actions"""
    from app.services.analytics_service import analytics_service, EventType

    # Track subscription upgrade event
    await analytics_service.track_event(
        event_type=EventType.SUBSCRIPTION_UPGRADE,
        user_id=user_id,
        data={
            "old_tier": old_tier,
            "new_tier": new_tier,
            "upgrade_timestamp": datetime.utcnow().isoformat()
        },
        subscription_tier=new_tier
    )

    # In a real implementation, this would also:
    # 1. Send upgrade confirmation email
    # 2. Update any cached user data
    # 3. Update billing records

    print(f"User {user_id} upgraded from {old_tier} to {new_tier}")
