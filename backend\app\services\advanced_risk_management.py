"""
Advanced risk management system with portfolio-level Greeks monitoring,
correlation analysis, and dynamic hedging
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from sqlalchemy import select, and_, func
from decimal import Decimal
import scipy.stats as stats
from scipy.optimize import minimize

from app.core.config import settings
from app.core.database import get_db
from app.models.trade import Trade, Position
from app.models.options import OptionPosition, OptionContract, OptionStrategy
from app.models.stock import Stock
from app.models.user import BrokerAccount
from app.services.data_provider import DataProvider

logger = logging.getLogger(__name__)

class PortfolioGreeks:
    """Portfolio-level Greeks calculations and monitoring"""
    
    def __init__(self):
        self.data_provider = DataProvider()
    
    async def calculate_portfolio_greeks(
        self,
        user_id: int,
        broker_account_id: int
    ) -> Dict[str, Any]:
        """Calculate portfolio-level Greeks"""
        try:
            async with get_db() as db:
                # Get all option positions
                result = await db.execute(
                    select(OptionPosition).where(
                        and_(
                            OptionPosition.user_id == user_id,
                            OptionPosition.broker_account_id == broker_account_id,
                            OptionPosition.quantity != 0
                        )
                    )
                )
                positions = result.scalars().all()
                
                if not positions:
                    return self._empty_greeks_response()
                
                # Aggregate Greeks
                total_delta = 0.0
                total_gamma = 0.0
                total_theta = 0.0
                total_vega = 0.0
                total_rho = 0.0
                
                position_details = []
                
                for position in positions:
                    if (position.position_delta and position.position_gamma and 
                        position.position_theta and position.position_vega):
                        
                        total_delta += float(position.position_delta)
                        total_gamma += float(position.position_gamma)
                        total_theta += float(position.position_theta)
                        total_vega += float(position.position_vega)
                        total_rho += float(position.position_rho) if position.position_rho else 0.0
                        
                        position_details.append({
                            "symbol": position.option_contract.symbol if position.option_contract else "Unknown",
                            "quantity": position.quantity,
                            "delta": float(position.position_delta),
                            "gamma": float(position.position_gamma),
                            "theta": float(position.position_theta),
                            "vega": float(position.position_vega),
                            "rho": float(position.position_rho) if position.position_rho else 0.0
                        })
                
                # Calculate risk metrics
                delta_risk = self._calculate_delta_risk(total_delta)
                gamma_risk = self._calculate_gamma_risk(total_gamma)
                theta_decay = self._calculate_theta_decay(total_theta)
                vega_risk = self._calculate_vega_risk(total_vega)
                
                return {
                    "portfolio_greeks": {
                        "total_delta": total_delta,
                        "total_gamma": total_gamma,
                        "total_theta": total_theta,
                        "total_vega": total_vega,
                        "total_rho": total_rho
                    },
                    "risk_metrics": {
                        "delta_risk": delta_risk,
                        "gamma_risk": gamma_risk,
                        "theta_decay": theta_decay,
                        "vega_risk": vega_risk
                    },
                    "position_details": position_details,
                    "hedging_suggestions": await self._generate_hedging_suggestions(
                        total_delta, total_gamma, total_vega
                    )
                }
                
        except Exception as e:
            logger.error(f"Error calculating portfolio Greeks: {e}")
            return {"error": str(e)}
    
    def _empty_greeks_response(self) -> Dict[str, Any]:
        """Return empty Greeks response"""
        return {
            "portfolio_greeks": {
                "total_delta": 0.0,
                "total_gamma": 0.0,
                "total_theta": 0.0,
                "total_vega": 0.0,
                "total_rho": 0.0
            },
            "risk_metrics": {
                "delta_risk": "LOW",
                "gamma_risk": "LOW",
                "theta_decay": 0.0,
                "vega_risk": "LOW"
            },
            "position_details": [],
            "hedging_suggestions": []
        }
    
    def _calculate_delta_risk(self, total_delta: float) -> str:
        """Calculate delta risk level"""
        abs_delta = abs(total_delta)
        if abs_delta < 10:
            return "LOW"
        elif abs_delta < 50:
            return "MEDIUM"
        elif abs_delta < 100:
            return "HIGH"
        else:
            return "CRITICAL"
    
    def _calculate_gamma_risk(self, total_gamma: float) -> str:
        """Calculate gamma risk level"""
        abs_gamma = abs(total_gamma)
        if abs_gamma < 5:
            return "LOW"
        elif abs_gamma < 20:
            return "MEDIUM"
        elif abs_gamma < 50:
            return "HIGH"
        else:
            return "CRITICAL"
    
    def _calculate_theta_decay(self, total_theta: float) -> float:
        """Calculate daily theta decay"""
        return total_theta  # Theta is already daily decay
    
    def _calculate_vega_risk(self, total_vega: float) -> str:
        """Calculate vega risk level"""
        abs_vega = abs(total_vega)
        if abs_vega < 100:
            return "LOW"
        elif abs_vega < 500:
            return "MEDIUM"
        elif abs_vega < 1000:
            return "HIGH"
        else:
            return "CRITICAL"
    
    async def _generate_hedging_suggestions(
        self,
        total_delta: float,
        total_gamma: float,
        total_vega: float
    ) -> List[Dict[str, Any]]:
        """Generate hedging suggestions based on Greeks exposure"""
        suggestions = []
        
        # Delta hedging
        if abs(total_delta) > 25:
            direction = "sell" if total_delta > 0 else "buy"
            shares_needed = int(abs(total_delta))
            suggestions.append({
                "type": "delta_hedge",
                "action": f"{direction} {shares_needed} shares of underlying",
                "reason": f"Portfolio delta of {total_delta:.2f} exceeds threshold",
                "priority": "HIGH" if abs(total_delta) > 50 else "MEDIUM"
            })
        
        # Gamma hedging
        if abs(total_gamma) > 30:
            suggestions.append({
                "type": "gamma_hedge",
                "action": "Consider long straddles or strangles to reduce gamma exposure",
                "reason": f"Portfolio gamma of {total_gamma:.2f} creates high convexity risk",
                "priority": "HIGH" if abs(total_gamma) > 50 else "MEDIUM"
            })
        
        # Vega hedging
        if abs(total_vega) > 750:
            direction = "short" if total_vega > 0 else "long"
            suggestions.append({
                "type": "vega_hedge",
                "action": f"Consider {direction} volatility positions",
                "reason": f"Portfolio vega of {total_vega:.2f} creates high volatility risk",
                "priority": "HIGH" if abs(total_vega) > 1000 else "MEDIUM"
            })
        
        return suggestions


class CorrelationAnalyzer:
    """Portfolio correlation analysis and risk assessment"""
    
    def __init__(self):
        self.data_provider = DataProvider()
    
    async def analyze_portfolio_correlations(
        self,
        user_id: int,
        broker_account_id: int,
        lookback_days: int = 60
    ) -> Dict[str, Any]:
        """Analyze correlations between portfolio positions"""
        try:
            async with get_db() as db:
                # Get all stock positions
                result = await db.execute(
                    select(Position).where(
                        and_(
                            Position.user_id == user_id,
                            Position.broker_account_id == broker_account_id,
                            Position.quantity != 0
                        )
                    )
                )
                positions = result.scalars().all()
                
                if len(positions) < 2:
                    return {"error": "Need at least 2 positions for correlation analysis"}
                
                # Get symbols and weights
                symbols = []
                weights = []
                total_value = 0.0
                
                for position in positions:
                    if position.stock and position.market_value:
                        symbols.append(position.stock.symbol)
                        market_value = float(position.market_value)
                        weights.append(market_value)
                        total_value += market_value
                
                # Normalize weights
                weights = [w / total_value for w in weights]
                
                # Get historical data for all symbols
                historical_data = {}
                for symbol in symbols:
                    data = await self.data_provider.get_historical_data(
                        symbol, period=f"{lookback_days}d", interval="1d"
                    )
                    if data is not None and len(data) > 0:
                        historical_data[symbol] = data['close'].pct_change().dropna()
                
                if len(historical_data) < 2:
                    return {"error": "Insufficient historical data for correlation analysis"}
                
                # Create returns DataFrame
                returns_df = pd.DataFrame(historical_data)
                returns_df = returns_df.dropna()
                
                if len(returns_df) < 20:
                    return {"error": "Insufficient overlapping data for correlation analysis"}
                
                # Calculate correlation matrix
                correlation_matrix = returns_df.corr()
                
                # Calculate portfolio risk metrics
                portfolio_variance = self._calculate_portfolio_variance(returns_df, weights)
                diversification_ratio = self._calculate_diversification_ratio(returns_df, weights)
                concentration_risk = self._calculate_concentration_risk(weights)
                
                # Find highly correlated pairs
                high_correlations = self._find_high_correlations(correlation_matrix, threshold=0.7)
                
                return {
                    "correlation_matrix": correlation_matrix.to_dict(),
                    "portfolio_metrics": {
                        "portfolio_variance": portfolio_variance,
                        "portfolio_volatility": np.sqrt(portfolio_variance * 252),  # Annualized
                        "diversification_ratio": diversification_ratio,
                        "concentration_risk": concentration_risk
                    },
                    "high_correlations": high_correlations,
                    "risk_warnings": self._generate_correlation_warnings(
                        high_correlations, concentration_risk, diversification_ratio
                    ),
                    "symbols": symbols,
                    "weights": weights
                }
                
        except Exception as e:
            logger.error(f"Error analyzing portfolio correlations: {e}")
            return {"error": str(e)}
    
    def _calculate_portfolio_variance(self, returns_df: pd.DataFrame, weights: List[float]) -> float:
        """Calculate portfolio variance"""
        try:
            cov_matrix = returns_df.cov()
            weights_array = np.array(weights)
            portfolio_variance = np.dot(weights_array.T, np.dot(cov_matrix, weights_array))
            return float(portfolio_variance)
        except Exception as e:
            logger.error(f"Error calculating portfolio variance: {e}")
            return 0.0
    
    def _calculate_diversification_ratio(self, returns_df: pd.DataFrame, weights: List[float]) -> float:
        """Calculate diversification ratio"""
        try:
            # Weighted average of individual volatilities
            individual_vols = returns_df.std() * np.sqrt(252)  # Annualized
            weighted_avg_vol = np.sum(np.array(weights) * individual_vols)
            
            # Portfolio volatility
            portfolio_var = self._calculate_portfolio_variance(returns_df, weights)
            portfolio_vol = np.sqrt(portfolio_var * 252)
            
            return float(weighted_avg_vol / portfolio_vol) if portfolio_vol > 0 else 1.0
        except Exception as e:
            logger.error(f"Error calculating diversification ratio: {e}")
            return 1.0
    
    def _calculate_concentration_risk(self, weights: List[float]) -> float:
        """Calculate concentration risk using Herfindahl-Hirschman Index"""
        try:
            hhi = sum(w**2 for w in weights)
            return float(hhi)
        except Exception as e:
            logger.error(f"Error calculating concentration risk: {e}")
            return 0.0
    
    def _find_high_correlations(self, corr_matrix: pd.DataFrame, threshold: float = 0.7) -> List[Dict[str, Any]]:
        """Find pairs with high correlation"""
        high_corrs = []
        
        for i in range(len(corr_matrix.columns)):
            for j in range(i + 1, len(corr_matrix.columns)):
                corr_value = corr_matrix.iloc[i, j]
                if abs(corr_value) >= threshold:
                    high_corrs.append({
                        "symbol1": corr_matrix.columns[i],
                        "symbol2": corr_matrix.columns[j],
                        "correlation": float(corr_value),
                        "risk_level": "HIGH" if abs(corr_value) > 0.8 else "MEDIUM"
                    })
        
        return sorted(high_corrs, key=lambda x: abs(x["correlation"]), reverse=True)
    
    def _generate_correlation_warnings(
        self,
        high_correlations: List[Dict[str, Any]],
        concentration_risk: float,
        diversification_ratio: float
    ) -> List[Dict[str, Any]]:
        """Generate risk warnings based on correlation analysis"""
        warnings = []
        
        # High correlation warnings
        for corr in high_correlations[:3]:  # Top 3 high correlations
            if abs(corr["correlation"]) > 0.8:
                warnings.append({
                    "type": "HIGH_CORRELATION",
                    "message": f"High correlation ({corr['correlation']:.2f}) between {corr['symbol1']} and {corr['symbol2']}",
                    "severity": "HIGH",
                    "recommendation": "Consider reducing position size in one of these correlated assets"
                })
        
        # Concentration risk warning
        if concentration_risk > 0.25:  # HHI > 0.25 indicates high concentration
            warnings.append({
                "type": "CONCENTRATION_RISK",
                "message": f"High portfolio concentration (HHI: {concentration_risk:.3f})",
                "severity": "HIGH" if concentration_risk > 0.4 else "MEDIUM",
                "recommendation": "Consider diversifying into additional uncorrelated assets"
            })
        
        # Low diversification warning
        if diversification_ratio < 1.2:
            warnings.append({
                "type": "LOW_DIVERSIFICATION",
                "message": f"Low diversification benefit (ratio: {diversification_ratio:.2f})",
                "severity": "MEDIUM",
                "recommendation": "Portfolio may benefit from additional diversification"
            })
        
        return warnings


class DynamicHedging:
    """Dynamic hedging system for portfolio risk management"""

    def __init__(self):
        self.data_provider = DataProvider()
        self.portfolio_greeks = PortfolioGreeks()

    async def calculate_hedge_requirements(
        self,
        user_id: int,
        broker_account_id: int,
        target_delta: float = 0.0,
        target_gamma: float = 0.0,
        target_vega: float = 0.0
    ) -> Dict[str, Any]:
        """Calculate required hedges to achieve target Greeks"""
        try:
            # Get current portfolio Greeks
            current_greeks = await self.portfolio_greeks.calculate_portfolio_greeks(
                user_id, broker_account_id
            )

            if "error" in current_greeks:
                return current_greeks

            portfolio_greeks = current_greeks["portfolio_greeks"]

            # Calculate required adjustments
            delta_adjustment = target_delta - portfolio_greeks["total_delta"]
            gamma_adjustment = target_gamma - portfolio_greeks["total_gamma"]
            vega_adjustment = target_vega - portfolio_greeks["total_vega"]

            # Generate hedge recommendations
            hedge_recommendations = []

            # Delta hedge with underlying stock
            if abs(delta_adjustment) > 5:
                hedge_recommendations.append({
                    "type": "delta_hedge",
                    "instrument": "underlying_stock",
                    "action": "buy" if delta_adjustment > 0 else "sell",
                    "quantity": int(abs(delta_adjustment)),
                    "expected_delta_change": delta_adjustment,
                    "cost_estimate": await self._estimate_hedge_cost("stock", abs(delta_adjustment)),
                    "priority": "HIGH" if abs(delta_adjustment) > 25 else "MEDIUM"
                })

            # Gamma hedge with options
            if abs(gamma_adjustment) > 10:
                hedge_recommendations.append({
                    "type": "gamma_hedge",
                    "instrument": "options",
                    "action": "buy_straddle" if gamma_adjustment > 0 else "sell_straddle",
                    "expected_gamma_change": gamma_adjustment,
                    "cost_estimate": await self._estimate_hedge_cost("options", abs(gamma_adjustment)),
                    "priority": "HIGH" if abs(gamma_adjustment) > 30 else "MEDIUM"
                })

            # Vega hedge with options
            if abs(vega_adjustment) > 100:
                hedge_recommendations.append({
                    "type": "vega_hedge",
                    "instrument": "options",
                    "action": "buy_options" if vega_adjustment > 0 else "sell_options",
                    "expected_vega_change": vega_adjustment,
                    "cost_estimate": await self._estimate_hedge_cost("vega", abs(vega_adjustment)),
                    "priority": "HIGH" if abs(vega_adjustment) > 500 else "MEDIUM"
                })

            return {
                "current_greeks": portfolio_greeks,
                "target_greeks": {
                    "target_delta": target_delta,
                    "target_gamma": target_gamma,
                    "target_vega": target_vega
                },
                "required_adjustments": {
                    "delta_adjustment": delta_adjustment,
                    "gamma_adjustment": gamma_adjustment,
                    "vega_adjustment": vega_adjustment
                },
                "hedge_recommendations": hedge_recommendations,
                "total_estimated_cost": sum(rec.get("cost_estimate", 0) for rec in hedge_recommendations)
            }

        except Exception as e:
            logger.error(f"Error calculating hedge requirements: {e}")
            return {"error": str(e)}

    async def _estimate_hedge_cost(self, instrument_type: str, quantity: float) -> float:
        """Estimate cost of hedge"""
        try:
            if instrument_type == "stock":
                # Assume average stock price of $100 and minimal slippage
                return quantity * 100 * 0.001  # 0.1% slippage
            elif instrument_type == "options":
                # Rough estimate for options hedging
                return quantity * 50  # $50 per gamma unit
            elif instrument_type == "vega":
                # Rough estimate for vega hedging
                return quantity * 0.10  # $0.10 per vega unit
            else:
                return 0.0
        except Exception:
            return 0.0

    async def execute_dynamic_hedge(
        self,
        user_id: int,
        broker_account_id: int,
        hedge_recommendation: Dict[str, Any],
        is_paper_trade: bool = True
    ) -> Dict[str, Any]:
        """Execute a dynamic hedge recommendation"""
        try:
            # This would integrate with the trading engine to execute hedges
            # For now, return a simulation of the hedge execution

            return {
                "success": True,
                "hedge_type": hedge_recommendation["type"],
                "executed_action": hedge_recommendation["action"],
                "estimated_cost": hedge_recommendation.get("cost_estimate", 0),
                "is_paper_trade": is_paper_trade,
                "execution_time": datetime.utcnow().isoformat(),
                "message": f"Dynamic hedge executed: {hedge_recommendation['action']}"
            }

        except Exception as e:
            logger.error(f"Error executing dynamic hedge: {e}")
            return {"error": str(e)}


class AdvancedRiskManager:
    """Main advanced risk management coordinator"""

    def __init__(self):
        self.portfolio_greeks = PortfolioGreeks()
        self.correlation_analyzer = CorrelationAnalyzer()
        self.dynamic_hedging = DynamicHedging()

    async def comprehensive_risk_assessment(
        self,
        user_id: int,
        broker_account_id: int
    ) -> Dict[str, Any]:
        """Perform comprehensive risk assessment"""
        try:
            # Run all risk analyses in parallel
            greeks_task = self.portfolio_greeks.calculate_portfolio_greeks(user_id, broker_account_id)
            correlation_task = self.correlation_analyzer.analyze_portfolio_correlations(user_id, broker_account_id)
            hedge_task = self.dynamic_hedging.calculate_hedge_requirements(user_id, broker_account_id)

            greeks_result, correlation_result, hedge_result = await asyncio.gather(
                greeks_task, correlation_task, hedge_task, return_exceptions=True
            )

            # Compile comprehensive risk report
            risk_report = {
                "timestamp": datetime.utcnow().isoformat(),
                "user_id": user_id,
                "broker_account_id": broker_account_id,
                "greeks_analysis": greeks_result if not isinstance(greeks_result, Exception) else {"error": str(greeks_result)},
                "correlation_analysis": correlation_result if not isinstance(correlation_result, Exception) else {"error": str(correlation_result)},
                "hedging_analysis": hedge_result if not isinstance(hedge_result, Exception) else {"error": str(hedge_result)},
                "overall_risk_score": self._calculate_overall_risk_score(greeks_result, correlation_result),
                "priority_actions": self._generate_priority_actions(greeks_result, correlation_result, hedge_result)
            }

            return risk_report

        except Exception as e:
            logger.error(f"Error in comprehensive risk assessment: {e}")
            return {"error": str(e)}

    def _calculate_overall_risk_score(
        self,
        greeks_result: Dict[str, Any],
        correlation_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate overall portfolio risk score"""
        try:
            risk_factors = []

            # Greeks risk factors
            if "risk_metrics" in greeks_result:
                risk_metrics = greeks_result["risk_metrics"]

                risk_scores = {
                    "LOW": 1, "MEDIUM": 2, "HIGH": 3, "CRITICAL": 4
                }

                delta_score = risk_scores.get(risk_metrics.get("delta_risk", "LOW"), 1)
                gamma_score = risk_scores.get(risk_metrics.get("gamma_risk", "LOW"), 1)
                vega_score = risk_scores.get(risk_metrics.get("vega_risk", "LOW"), 1)

                risk_factors.extend([delta_score, gamma_score, vega_score])

            # Correlation risk factors
            if "portfolio_metrics" in correlation_result:
                metrics = correlation_result["portfolio_metrics"]

                # Concentration risk
                concentration = metrics.get("concentration_risk", 0)
                if concentration > 0.4:
                    risk_factors.append(4)
                elif concentration > 0.25:
                    risk_factors.append(3)
                else:
                    risk_factors.append(1)

                # Diversification ratio
                div_ratio = metrics.get("diversification_ratio", 1.0)
                if div_ratio < 1.2:
                    risk_factors.append(3)
                elif div_ratio < 1.5:
                    risk_factors.append(2)
                else:
                    risk_factors.append(1)

            # Calculate overall score
            if risk_factors:
                avg_score = sum(risk_factors) / len(risk_factors)
                max_score = max(risk_factors)

                if max_score >= 4 or avg_score >= 3.5:
                    overall_level = "CRITICAL"
                elif max_score >= 3 or avg_score >= 2.5:
                    overall_level = "HIGH"
                elif avg_score >= 2.0:
                    overall_level = "MEDIUM"
                else:
                    overall_level = "LOW"
            else:
                overall_level = "LOW"
                avg_score = 1.0

            return {
                "overall_level": overall_level,
                "score": round(avg_score, 2),
                "risk_factors_count": len(risk_factors),
                "max_individual_risk": max(risk_factors) if risk_factors else 1
            }

        except Exception as e:
            logger.error(f"Error calculating overall risk score: {e}")
            return {"overall_level": "UNKNOWN", "score": 0.0}

    def _generate_priority_actions(
        self,
        greeks_result: Dict[str, Any],
        correlation_result: Dict[str, Any],
        hedge_result: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate priority actions based on risk analysis"""
        actions = []

        try:
            # High priority hedging actions
            if "hedge_recommendations" in hedge_result:
                for hedge in hedge_result["hedge_recommendations"]:
                    if hedge.get("priority") == "HIGH":
                        actions.append({
                            "type": "HEDGE",
                            "priority": "HIGH",
                            "action": hedge["action"],
                            "description": f"Execute {hedge['type']} to manage risk exposure",
                            "estimated_cost": hedge.get("cost_estimate", 0)
                        })

            # Correlation-based actions
            if "risk_warnings" in correlation_result:
                for warning in correlation_result["risk_warnings"]:
                    if warning.get("severity") == "HIGH":
                        actions.append({
                            "type": "DIVERSIFICATION",
                            "priority": "HIGH",
                            "action": warning["recommendation"],
                            "description": warning["message"]
                        })

            # Greeks-based actions
            if "hedging_suggestions" in greeks_result:
                for suggestion in greeks_result["hedging_suggestions"]:
                    if suggestion.get("priority") == "HIGH":
                        actions.append({
                            "type": "GREEKS_MANAGEMENT",
                            "priority": "HIGH",
                            "action": suggestion["action"],
                            "description": suggestion["reason"]
                        })

            # Sort by priority
            priority_order = {"HIGH": 1, "MEDIUM": 2, "LOW": 3}
            actions.sort(key=lambda x: priority_order.get(x.get("priority", "LOW"), 3))

            return actions[:10]  # Return top 10 priority actions

        except Exception as e:
            logger.error(f"Error generating priority actions: {e}")
            return []
