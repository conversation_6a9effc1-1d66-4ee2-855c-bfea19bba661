"""
Trading models for orders, positions, and trade history
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, JSON, Enum, Index, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict, Any, Optional
import enum

from app.core.database import Base

class OrderType(enum.Enum):
    """Order type enumeration"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"

class OrderSide(enum.Enum):
    """Order side enumeration"""
    BUY = "buy"
    SELL = "sell"
    BUY_TO_COVER = "buy_to_cover"
    SELL_SHORT = "sell_short"

class OrderStatus(enum.Enum):
    """Order status enumeration"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"

class PositionType(enum.Enum):
    """Position type enumeration"""
    LONG = "long"
    SHORT = "short"

class Trade(Base):
    """Trade execution model"""
    
    __tablename__ = "trades"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False, index=True)
    broker_account_id = Column(Integer, ForeignKey("broker_accounts.id"), nullable=False, index=True)
    portfolio_id = Column(Integer, ForeignKey("portfolios.id"), index=True)  # Optional portfolio reference
    strategy_id = Column(Integer, ForeignKey("strategies.id"), index=True)  # Optional strategy reference
    
    # Order information
    broker_order_id = Column(String(100), index=True)  # Broker's order ID
    order_type = Column(Enum(OrderType), nullable=False)
    order_side = Column(Enum(OrderSide), nullable=False)
    
    # Quantities and prices
    quantity = Column(Integer, nullable=False)
    price = Column(Float)  # Limit price for limit orders
    stop_price = Column(Float)  # Stop price for stop orders
    filled_quantity = Column(Integer, default=0)
    average_fill_price = Column(Float)
    
    # Order status
    status = Column(Enum(OrderStatus), default=OrderStatus.PENDING)
    rejection_reason = Column(Text)  # Reason for rejection
    time_in_force = Column(String(10), default="day")  # day, gtc, ioc, fok
    
    # Execution details
    submitted_at = Column(DateTime(timezone=True))
    filled_at = Column(DateTime(timezone=True))
    cancelled_at = Column(DateTime(timezone=True))
    
    # Financial details
    commission = Column(Float, default=0.0)
    fees = Column(Float, default=0.0)
    total_cost = Column(Float)  # Total cost including fees
    realized_pnl = Column(Float)  # Realized profit/loss
    
    # Risk management
    stop_loss_price = Column(Float)
    take_profit_price = Column(Float)
    
    # Metadata
    notes = Column(Text)
    broker_metadata = Column(JSON)  # Additional broker-specific data
    
    # Paper trading flag
    is_paper_trade = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="trades")
    stock = relationship("Stock", back_populates="trades")
    broker_account = relationship("BrokerAccount", back_populates="trades")
    strategy = relationship("Strategy", back_populates="trades")
    
    # Indexes
    __table_args__ = (
        Index('idx_trade_user_date', 'user_id', 'created_at'),
        Index('idx_trade_status', 'status'),
        Index('idx_trade_broker_order', 'broker_order_id'),
    )
    
    def __repr__(self):
        return f"<Trade(id={self.id}, symbol='{self.stock.symbol if self.stock else 'N/A'}', side='{self.order_side.value}', quantity={self.quantity})>"
    
    @property
    def is_buy_order(self) -> bool:
        """Check if this is a buy order"""
        return self.order_side in [OrderSide.BUY, OrderSide.BUY_TO_COVER]
    
    @property
    def is_filled(self) -> bool:
        """Check if order is completely filled"""
        return self.status == OrderStatus.FILLED
    
    @property
    def is_active(self) -> bool:
        """Check if order is still active"""
        return self.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED]
    
    @property
    def fill_percentage(self) -> float:
        """Get fill percentage"""
        if self.quantity == 0:
            return 0.0
        return (self.filled_quantity / self.quantity) * 100
    
    @property
    def unrealized_pnl(self) -> Optional[float]:
        """Calculate unrealized P&L for filled trades"""
        if not self.is_filled or not self.stock or not self.stock.current_price:
            return None
        
        if self.order_side == OrderSide.BUY:
            return (self.stock.current_price - self.average_fill_price) * self.filled_quantity
        else:
            return (self.average_fill_price - self.stock.current_price) * self.filled_quantity
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert trade to dictionary"""
        return {
            "id": self.id,
            "broker_order_id": self.broker_order_id,
            "order_type": self.order_type.value,
            "order_side": self.order_side.value,
            "quantity": self.quantity,
            "price": self.price,
            "stop_price": self.stop_price,
            "filled_quantity": self.filled_quantity,
            "average_fill_price": self.average_fill_price,
            "status": self.status.value,
            "commission": self.commission,
            "fees": self.fees,
            "total_cost": self.total_cost,
            "stop_loss_price": self.stop_loss_price,
            "take_profit_price": self.take_profit_price,
            "is_paper_trade": self.is_paper_trade,
            "fill_percentage": self.fill_percentage,
            "unrealized_pnl": self.unrealized_pnl,
            "submitted_at": self.submitted_at.isoformat() if self.submitted_at else None,
            "filled_at": self.filled_at.isoformat() if self.filled_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "stock": self.stock.to_dict() if self.stock else None,
        }

class Position(Base):
    """Current position model"""
    
    __tablename__ = "positions"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False, index=True)
    broker_account_id = Column(Integer, ForeignKey("broker_accounts.id"), nullable=False, index=True)
    portfolio_id = Column(Integer, ForeignKey("portfolios.id"), index=True)  # Optional portfolio reference
    
    # Position details
    position_type = Column(Enum(PositionType), nullable=False)
    quantity = Column(Integer, nullable=False)
    average_cost = Column(Float, nullable=False)
    
    # Current market value
    current_price = Column(Float)
    market_value = Column(Float)
    
    # P&L calculations
    unrealized_pnl = Column(Float)
    unrealized_pnl_percent = Column(Float)
    day_pnl = Column(Float)
    day_pnl_percent = Column(Float)
    
    # Risk management
    stop_loss_price = Column(Float)
    take_profit_price = Column(Float)
    
    # Position metadata
    opened_at = Column(DateTime(timezone=True), nullable=False)
    last_updated = Column(DateTime(timezone=True), server_default=func.now())
    
    # Paper trading flag
    is_paper_position = Column(Boolean, default=True)
    
    # Timestamps
    opened_at = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User")
    stock = relationship("Stock")
    broker_account = relationship("BrokerAccount")
    portfolio = relationship("Portfolio")

    # Indexes
    __table_args__ = (
        Index('idx_position_user_stock', 'user_id', 'stock_id'),
        Index('idx_position_broker_account', 'broker_account_id'),
    )
    
    def __repr__(self):
        return f"<Position(id={self.id}, stock_id={self.stock_id}, type='{self.position_type.value}', quantity={self.quantity})>"
    
    @property
    def is_long(self) -> bool:
        """Check if this is a long position"""
        return self.position_type == PositionType.LONG
    
    @property
    def is_profitable(self) -> bool:
        """Check if position is currently profitable"""
        return self.unrealized_pnl is not None and self.unrealized_pnl > 0
    
    @property
    def total_cost(self) -> float:
        """Get total cost of position"""
        return self.average_cost * abs(self.quantity)
    
    def update_market_value(self, current_price: float):
        """Update position market value and P&L"""
        self.current_price = current_price
        self.market_value = current_price * abs(self.quantity)
        
        if self.position_type == PositionType.LONG:
            self.unrealized_pnl = (current_price - self.average_cost) * self.quantity
        else:
            self.unrealized_pnl = (self.average_cost - current_price) * abs(self.quantity)
        
        if self.average_cost > 0:
            self.unrealized_pnl_percent = (self.unrealized_pnl / self.total_cost) * 100
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert position to dictionary"""
        return {
            "id": self.id,
            "position_type": self.position_type.value,
            "quantity": self.quantity,
            "average_cost": self.average_cost,
            "current_price": self.current_price,
            "market_value": self.market_value,
            "total_cost": self.total_cost,
            "unrealized_pnl": self.unrealized_pnl,
            "unrealized_pnl_percent": self.unrealized_pnl_percent,
            "day_pnl": self.day_pnl,
            "day_pnl_percent": self.day_pnl_percent,
            "stop_loss_price": self.stop_loss_price,
            "take_profit_price": self.take_profit_price,
            "is_paper_position": self.is_paper_position,
            "is_profitable": self.is_profitable,
            "opened_at": self.opened_at.isoformat() if self.opened_at else None,
            "last_updated": self.last_updated.isoformat() if self.last_updated else None,
        }

class TradeHistory(Base):
    """Completed trade history for performance analysis"""
    
    __tablename__ = "trade_history"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False, index=True)
    strategy_id = Column(Integer, ForeignKey("strategies.id"), index=True)
    
    # Trade details
    entry_date = Column(DateTime(timezone=True), nullable=False)
    exit_date = Column(DateTime(timezone=True), nullable=False)
    position_type = Column(Enum(PositionType), nullable=False)
    
    # Entry details
    entry_price = Column(Float, nullable=False)
    entry_quantity = Column(Integer, nullable=False)
    entry_commission = Column(Float, default=0.0)
    
    # Exit details
    exit_price = Column(Float, nullable=False)
    exit_quantity = Column(Integer, nullable=False)
    exit_commission = Column(Float, default=0.0)
    
    # P&L calculations
    gross_pnl = Column(Float, nullable=False)
    net_pnl = Column(Float, nullable=False)
    pnl_percent = Column(Float, nullable=False)
    
    # Trade duration
    hold_duration_days = Column(Float)
    
    # Trade classification
    is_winning_trade = Column(Boolean)
    is_paper_trade = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User")
    stock = relationship("Stock")
    strategy = relationship("Strategy")

    # Indexes
    __table_args__ = (
        Index('idx_trade_history_user_date', 'user_id', 'exit_date'),
        Index('idx_trade_history_performance', 'is_winning_trade', 'pnl_percent'),
    )
    
    def __repr__(self):
        return f"<TradeHistory(id={self.id}, stock_id={self.stock_id}, pnl={self.net_pnl})>"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert trade history to dictionary"""
        return {
            "id": self.id,
            "entry_date": self.entry_date.isoformat(),
            "exit_date": self.exit_date.isoformat(),
            "position_type": self.position_type.value,
            "entry_price": self.entry_price,
            "entry_quantity": self.entry_quantity,
            "exit_price": self.exit_price,
            "exit_quantity": self.exit_quantity,
            "gross_pnl": self.gross_pnl,
            "net_pnl": self.net_pnl,
            "pnl_percent": self.pnl_percent,
            "hold_duration_days": self.hold_duration_days,
            "is_winning_trade": self.is_winning_trade,
            "is_paper_trade": self.is_paper_trade,
        }


