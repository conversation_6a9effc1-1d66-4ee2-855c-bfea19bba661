"""
Advanced portfolio analytics including Sharpe ratio, alpha/beta calculations,
drawdown analysis, and performance attribution
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from sqlalchemy import select, and_, func
from decimal import Decimal
import scipy.stats as stats

from app.core.config import settings
from app.core.database import get_db
from app.models.trade import Trade, TradeHistory, Position
from app.models.portfolio import Portfolio
from app.models.stock import Stock
from app.models.user import BrokerAccount
from app.services.data_provider import DataProvider

logger = logging.getLogger(__name__)

class PortfolioAnalytics:
    """Advanced portfolio analytics and performance metrics"""
    
    def __init__(self):
        self.data_provider = DataProvider()
    
    async def calculate_portfolio_performance(
        self,
        user_id: int,
        broker_account_id: int,
        start_date: datetime,
        end_date: datetime,
        benchmark_symbol: str = "SPY"
    ) -> Dict[str, Any]:
        """Calculate comprehensive portfolio performance metrics"""
        try:
            async with get_db() as db:
                # Get portfolio trades in date range
                result = await db.execute(
                    select(TradeHistory).where(
                        and_(
                            TradeHistory.user_id == user_id,
                            TradeHistory.broker_account_id == broker_account_id,
                            TradeHistory.entry_date >= start_date,
                            TradeHistory.exit_date <= end_date
                        )
                    ).order_by(TradeHistory.exit_date)
                )
                trades = result.scalars().all()
                
                if not trades:
                    return {"error": "No trades found in the specified period"}
                
                # Calculate daily returns
                daily_returns = await self._calculate_daily_returns(trades, start_date, end_date)
                
                # Get benchmark returns
                benchmark_returns = await self._get_benchmark_returns(benchmark_symbol, start_date, end_date)
                
                # Calculate performance metrics
                total_return = self._calculate_total_return(trades)
                annualized_return = self._calculate_annualized_return(daily_returns)
                volatility = self._calculate_volatility(daily_returns)
                sharpe_ratio = self._calculate_sharpe_ratio(daily_returns)
                max_drawdown = self._calculate_max_drawdown(daily_returns)
                
                # Calculate alpha and beta
                alpha, beta = self._calculate_alpha_beta(daily_returns, benchmark_returns)
                
                # Calculate additional metrics
                win_rate = self._calculate_win_rate(trades)
                profit_factor = self._calculate_profit_factor(trades)
                calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
                sortino_ratio = self._calculate_sortino_ratio(daily_returns)
                
                # Performance attribution
                sector_attribution = await self._calculate_sector_attribution(trades)
                
                return {
                    "period": {
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat(),
                        "days": (end_date - start_date).days
                    },
                    "returns": {
                        "total_return": total_return,
                        "annualized_return": annualized_return,
                        "benchmark_return": self._calculate_benchmark_total_return(benchmark_returns)
                    },
                    "risk_metrics": {
                        "volatility": volatility,
                        "max_drawdown": max_drawdown,
                        "sharpe_ratio": sharpe_ratio,
                        "sortino_ratio": sortino_ratio,
                        "calmar_ratio": calmar_ratio,
                        "beta": beta,
                        "alpha": alpha
                    },
                    "trade_metrics": {
                        "total_trades": len(trades),
                        "win_rate": win_rate,
                        "profit_factor": profit_factor,
                        "average_win": np.mean([t.net_pnl for t in trades if t.net_pnl > 0]) if any(t.net_pnl > 0 for t in trades) else 0,
                        "average_loss": np.mean([t.net_pnl for t in trades if t.net_pnl < 0]) if any(t.net_pnl < 0 for t in trades) else 0
                    },
                    "attribution": {
                        "sector_attribution": sector_attribution
                    },
                    "daily_returns": daily_returns.tolist() if isinstance(daily_returns, np.ndarray) else daily_returns
                }
                
        except Exception as e:
            logger.error(f"Error calculating portfolio performance: {e}")
            return {"error": str(e)}
    
    async def _calculate_daily_returns(
        self,
        trades: List[TradeHistory],
        start_date: datetime,
        end_date: datetime
    ) -> np.ndarray:
        """Calculate daily portfolio returns"""
        try:
            # Create date range
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            daily_pnl = pd.Series(0.0, index=date_range)
            
            # Aggregate P&L by date
            for trade in trades:
                if trade.exit_date and trade.net_pnl:
                    exit_date = trade.exit_date.date()
                    if exit_date in daily_pnl.index.date:
                        daily_pnl[exit_date] += float(trade.net_pnl)
            
            # Calculate returns (assuming starting capital of $100,000)
            starting_capital = 100000.0
            cumulative_capital = starting_capital
            returns = []
            
            for pnl in daily_pnl:
                daily_return = pnl / cumulative_capital if cumulative_capital > 0 else 0
                returns.append(daily_return)
                cumulative_capital += pnl
            
            return np.array(returns)
            
        except Exception as e:
            logger.error(f"Error calculating daily returns: {e}")
            return np.array([])
    
    async def _get_benchmark_returns(
        self,
        benchmark_symbol: str,
        start_date: datetime,
        end_date: datetime
    ) -> np.ndarray:
        """Get benchmark daily returns"""
        try:
            # Get benchmark historical data
            days_diff = (end_date - start_date).days
            benchmark_data = await self.data_provider.get_historical_data(
                benchmark_symbol,
                period=f"{days_diff + 10}d",
                interval="1d"
            )
            
            if benchmark_data is None or len(benchmark_data) == 0:
                return np.array([])
            
            # Calculate daily returns
            returns = benchmark_data['close'].pct_change().dropna()
            
            # Filter to date range
            returns = returns[
                (returns.index.date >= start_date.date()) &
                (returns.index.date <= end_date.date())
            ]
            
            return returns.values
            
        except Exception as e:
            logger.error(f"Error getting benchmark returns: {e}")
            return np.array([])
    
    def _calculate_total_return(self, trades: List[TradeHistory]) -> float:
        """Calculate total return percentage"""
        try:
            total_pnl = sum(float(trade.net_pnl) for trade in trades if trade.net_pnl)
            total_invested = sum(float(trade.entry_price * trade.entry_quantity) for trade in trades)
            
            return (total_pnl / total_invested) * 100 if total_invested > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating total return: {e}")
            return 0.0
    
    def _calculate_annualized_return(self, daily_returns: np.ndarray) -> float:
        """Calculate annualized return"""
        try:
            if len(daily_returns) == 0:
                return 0.0
            
            cumulative_return = np.prod(1 + daily_returns) - 1
            days = len(daily_returns)
            annualized = (1 + cumulative_return) ** (252 / days) - 1
            
            return annualized * 100
            
        except Exception as e:
            logger.error(f"Error calculating annualized return: {e}")
            return 0.0
    
    def _calculate_volatility(self, daily_returns: np.ndarray) -> float:
        """Calculate annualized volatility"""
        try:
            if len(daily_returns) == 0:
                return 0.0
            
            return np.std(daily_returns) * np.sqrt(252) * 100
            
        except Exception as e:
            logger.error(f"Error calculating volatility: {e}")
            return 0.0
    
    def _calculate_sharpe_ratio(self, daily_returns: np.ndarray, risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio"""
        try:
            if len(daily_returns) == 0:
                return 0.0
            
            excess_returns = daily_returns - (risk_free_rate / 252)
            
            if np.std(excess_returns) == 0:
                return 0.0
            
            return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
            
        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0
    
    def _calculate_sortino_ratio(self, daily_returns: np.ndarray, risk_free_rate: float = 0.02) -> float:
        """Calculate Sortino ratio (downside deviation)"""
        try:
            if len(daily_returns) == 0:
                return 0.0
            
            excess_returns = daily_returns - (risk_free_rate / 252)
            downside_returns = excess_returns[excess_returns < 0]
            
            if len(downside_returns) == 0:
                return float('inf')
            
            downside_deviation = np.std(downside_returns)
            
            if downside_deviation == 0:
                return 0.0
            
            return np.mean(excess_returns) / downside_deviation * np.sqrt(252)
            
        except Exception as e:
            logger.error(f"Error calculating Sortino ratio: {e}")
            return 0.0
    
    def _calculate_max_drawdown(self, daily_returns: np.ndarray) -> float:
        """Calculate maximum drawdown"""
        try:
            if len(daily_returns) == 0:
                return 0.0
            
            cumulative_returns = np.cumprod(1 + daily_returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - running_max) / running_max
            
            return np.min(drawdown) * 100
            
        except Exception as e:
            logger.error(f"Error calculating max drawdown: {e}")
            return 0.0
    
    def _calculate_alpha_beta(
        self,
        portfolio_returns: np.ndarray,
        benchmark_returns: np.ndarray
    ) -> Tuple[float, float]:
        """Calculate alpha and beta vs benchmark"""
        try:
            if len(portfolio_returns) == 0 or len(benchmark_returns) == 0:
                return 0.0, 1.0
            
            # Align arrays to same length
            min_length = min(len(portfolio_returns), len(benchmark_returns))
            portfolio_returns = portfolio_returns[:min_length]
            benchmark_returns = benchmark_returns[:min_length]
            
            # Calculate beta using linear regression
            covariance = np.cov(portfolio_returns, benchmark_returns)[0, 1]
            benchmark_variance = np.var(benchmark_returns)
            
            beta = covariance / benchmark_variance if benchmark_variance != 0 else 1.0
            
            # Calculate alpha
            portfolio_mean = np.mean(portfolio_returns)
            benchmark_mean = np.mean(benchmark_returns)
            alpha = (portfolio_mean - beta * benchmark_mean) * 252 * 100  # Annualized
            
            return alpha, beta
            
        except Exception as e:
            logger.error(f"Error calculating alpha/beta: {e}")
            return 0.0, 1.0
    
    def _calculate_win_rate(self, trades: List[TradeHistory]) -> float:
        """Calculate win rate percentage"""
        try:
            if not trades:
                return 0.0
            
            winning_trades = sum(1 for trade in trades if trade.net_pnl and trade.net_pnl > 0)
            return (winning_trades / len(trades)) * 100
            
        except Exception as e:
            logger.error(f"Error calculating win rate: {e}")
            return 0.0
    
    def _calculate_profit_factor(self, trades: List[TradeHistory]) -> float:
        """Calculate profit factor (gross profit / gross loss)"""
        try:
            gross_profit = sum(float(trade.net_pnl) for trade in trades if trade.net_pnl and trade.net_pnl > 0)
            gross_loss = abs(sum(float(trade.net_pnl) for trade in trades if trade.net_pnl and trade.net_pnl < 0))
            
            return gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
        except Exception as e:
            logger.error(f"Error calculating profit factor: {e}")
            return 0.0
    
    def _calculate_benchmark_total_return(self, benchmark_returns: np.ndarray) -> float:
        """Calculate benchmark total return"""
        try:
            if len(benchmark_returns) == 0:
                return 0.0
            
            return (np.prod(1 + benchmark_returns) - 1) * 100
            
        except Exception as e:
            logger.error(f"Error calculating benchmark return: {e}")
            return 0.0
    
    async def _calculate_sector_attribution(self, trades: List[TradeHistory]) -> Dict[str, Any]:
        """Calculate performance attribution by sector"""
        try:
            async with get_db() as db:
                sector_performance = {}
                
                for trade in trades:
                    if trade.stock_id and trade.net_pnl:
                        # Get stock sector
                        result = await db.execute(
                            select(Stock).where(Stock.id == trade.stock_id)
                        )
                        stock = result.scalar_one_or_none()
                        
                        if stock and stock.sector:
                            sector = stock.sector
                            if sector not in sector_performance:
                                sector_performance[sector] = {
                                    "total_pnl": 0.0,
                                    "trade_count": 0,
                                    "win_count": 0
                                }
                            
                            pnl = float(trade.net_pnl)
                            sector_performance[sector]["total_pnl"] += pnl
                            sector_performance[sector]["trade_count"] += 1
                            
                            if pnl > 0:
                                sector_performance[sector]["win_count"] += 1
                
                # Calculate sector metrics
                for sector in sector_performance:
                    data = sector_performance[sector]
                    data["win_rate"] = (data["win_count"] / data["trade_count"]) * 100 if data["trade_count"] > 0 else 0
                    data["avg_pnl"] = data["total_pnl"] / data["trade_count"] if data["trade_count"] > 0 else 0
                
                return sector_performance
                
        except Exception as e:
            logger.error(f"Error calculating sector attribution: {e}")
            return {}

# Global instance
portfolio_analytics = PortfolioAnalytics()
