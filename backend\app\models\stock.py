"""
Stock and market data models
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, JSON, Index, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict, Any, Optional, List

from app.core.database import Base

class Stock(Base):
    """Stock information model"""
    
    __tablename__ = "stocks"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Stock identification
    symbol = Column(String(10), unique=True, index=True, nullable=False)
    name = Column(String(255), nullable=False)
    exchange = Column(String(10), nullable=False)  # NYSE, NASDAQ, etc.
    
    # Classification
    sector = Column(String(100))
    industry = Column(String(100))
    market_cap_category = Column(String(20))  # large, mid, small, micro
    
    # Current market data
    current_price = Column(Float)
    previous_close = Column(Float)
    day_change = Column(Float)
    day_change_percent = Column(Float)
    volume = Column(Integer)
    average_volume = Column(Integer)
    
    # Trading information
    bid_price = Column(Float)
    ask_price = Column(Float)
    bid_size = Column(Integer)
    ask_size = Column(Integer)
    
    # Market metrics
    market_cap = Column(Float)
    shares_outstanding = Column(Integer)
    float_shares = Column(Integer)
    
    # Fundamental data
    pe_ratio = Column(Float)
    pb_ratio = Column(Float)
    ps_ratio = Column(Float)
    peg_ratio = Column(Float)
    dividend_yield = Column(Float)
    eps = Column(Float)
    revenue = Column(Float)
    
    # Technical indicators (cached)
    rsi_14 = Column(Float)
    sma_20 = Column(Float)
    sma_50 = Column(Float)
    sma_200 = Column(Float)
    ema_12 = Column(Float)
    ema_26 = Column(Float)
    macd = Column(Float)
    macd_signal = Column(Float)
    bollinger_upper = Column(Float)
    bollinger_lower = Column(Float)
    
    # Status and metadata
    is_active = Column(Boolean, default=True)
    is_tradeable = Column(Boolean, default=True)
    last_updated = Column(DateTime(timezone=True))
    
    # Additional data
    additional_data = Column(JSON)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    screening_results = relationship("ScreeningResult", back_populates="stock")
    trades = relationship("Trade", back_populates="stock")
    watchlist_items = relationship("WatchlistItem", back_populates="stock")
    option_contracts = relationship("OptionContract", back_populates="underlying_stock")
    
    # Indexes
    __table_args__ = (
        Index('idx_stock_sector_industry', 'sector', 'industry'),
        Index('idx_stock_market_cap', 'market_cap'),
        Index('idx_stock_volume', 'volume'),
        Index('idx_stock_price', 'current_price'),
    )
    
    def __repr__(self):
        return f"<Stock(symbol='{self.symbol}', name='{self.name}', price={self.current_price})>"
    
    @property
    def price_change_direction(self) -> str:
        """Get price change direction"""
        if self.day_change is None:
            return "neutral"
        return "up" if self.day_change > 0 else "down" if self.day_change < 0 else "neutral"
    
    @property
    def is_above_sma_20(self) -> Optional[bool]:
        """Check if current price is above 20-day SMA"""
        if self.current_price is None or self.sma_20 is None:
            return None
        return self.current_price > self.sma_20
    
    @property
    def is_above_sma_50(self) -> Optional[bool]:
        """Check if current price is above 50-day SMA"""
        if self.current_price is None or self.sma_50 is None:
            return None
        return self.current_price > self.sma_50
    
    @property
    def is_above_sma_200(self) -> Optional[bool]:
        """Check if current price is above 200-day SMA"""
        if self.current_price is None or self.sma_200 is None:
            return None
        return self.current_price > self.sma_200
    
    @property
    def rsi_signal(self) -> Optional[str]:
        """Get RSI signal"""
        if self.rsi_14 is None:
            return None
        if self.rsi_14 > 70:
            return "overbought"
        elif self.rsi_14 < 30:
            return "oversold"
        else:
            return "neutral"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert stock to dictionary"""
        return {
            "id": self.id,
            "symbol": self.symbol,
            "name": self.name,
            "exchange": self.exchange,
            "sector": self.sector,
            "industry": self.industry,
            "market_cap_category": self.market_cap_category,
            "current_price": self.current_price,
            "previous_close": self.previous_close,
            "day_change": self.day_change,
            "day_change_percent": self.day_change_percent,
            "volume": self.volume,
            "average_volume": self.average_volume,
            "bid_price": self.bid_price,
            "ask_price": self.ask_price,
            "market_cap": self.market_cap,
            "pe_ratio": self.pe_ratio,
            "pb_ratio": self.pb_ratio,
            "dividend_yield": self.dividend_yield,
            "eps": self.eps,
            "rsi_14": self.rsi_14,
            "sma_20": self.sma_20,
            "sma_50": self.sma_50,
            "sma_200": self.sma_200,
            "macd": self.macd,
            "is_active": self.is_active,
            "is_tradeable": self.is_tradeable,
            "last_updated": self.last_updated.isoformat() if self.last_updated else None,
            "price_change_direction": self.price_change_direction,
            "is_above_sma_20": self.is_above_sma_20,
            "is_above_sma_50": self.is_above_sma_50,
            "is_above_sma_200": self.is_above_sma_200,
            "rsi_signal": self.rsi_signal,
        }

class MarketData(Base):
    """Historical market data model (for recent data, older data in InfluxDB)"""
    
    __tablename__ = "market_data"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Stock reference
    symbol = Column(String(10), index=True, nullable=False)
    
    # OHLCV data
    timestamp = Column(DateTime(timezone=True), nullable=False)
    open_price = Column(Float, nullable=False)
    high_price = Column(Float, nullable=False)
    low_price = Column(Float, nullable=False)
    close_price = Column(Float, nullable=False)
    volume = Column(Integer, nullable=False)
    
    # Adjusted data
    adjusted_close = Column(Float)
    
    # Timeframe
    timeframe = Column(String(10), nullable=False)  # 1m, 5m, 15m, 1h, 1d
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Indexes
    __table_args__ = (
        Index('idx_market_data_symbol_timestamp', 'symbol', 'timestamp'),
        Index('idx_market_data_timeframe', 'timeframe'),
    )
    
    def __repr__(self):
        return f"<MarketData(symbol='{self.symbol}', timestamp='{self.timestamp}', close={self.close_price})>"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert market data to dictionary"""
        return {
            "id": self.id,
            "symbol": self.symbol,
            "timestamp": self.timestamp.isoformat(),
            "open": self.open_price,
            "high": self.high_price,
            "low": self.low_price,
            "close": self.close_price,
            "volume": self.volume,
            "adjusted_close": self.adjusted_close,
            "timeframe": self.timeframe,
        }

class Watchlist(Base):
    """User watchlist model"""
    
    __tablename__ = "watchlists"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign key
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # Watchlist information
    name = Column(String(100), nullable=False)
    description = Column(Text)
    
    # Settings
    is_default = Column(Boolean, default=False)
    is_public = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="watchlists")
    items = relationship("WatchlistItem", back_populates="watchlist", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Watchlist(id={self.id}, name='{self.name}', user_id={self.user_id})>"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert watchlist to dictionary"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "is_default": self.is_default,
            "is_public": self.is_public,
            "item_count": len(self.items) if self.items else 0,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

class WatchlistItem(Base):
    """Watchlist item model"""
    
    __tablename__ = "watchlist_items"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign keys
    watchlist_id = Column(Integer, ForeignKey("watchlists.id"), nullable=False, index=True)
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False, index=True)
    
    # Item settings
    notes = Column(Text)
    target_price = Column(Float)
    stop_loss_price = Column(Float)
    
    # Alerts
    price_alert_enabled = Column(Boolean, default=False)
    volume_alert_enabled = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    watchlist = relationship("Watchlist", back_populates="items")
    stock = relationship("Stock", back_populates="watchlist_items")
    
    def __repr__(self):
        return f"<WatchlistItem(id={self.id}, watchlist_id={self.watchlist_id}, stock_id={self.stock_id})>"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert watchlist item to dictionary"""
        return {
            "id": self.id,
            "notes": self.notes,
            "target_price": self.target_price,
            "stop_loss_price": self.stop_loss_price,
            "price_alert_enabled": self.price_alert_enabled,
            "volume_alert_enabled": self.volume_alert_enabled,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "stock": self.stock.to_dict() if self.stock else None,
        }




class ScreeningResult(Base):
    """Stock screening result model"""

    __tablename__ = "screening_results"

    # Primary key
    id = Column(Integer, primary_key=True, index=True)

    # Foreign keys
    strategy_id = Column(Integer, ForeignKey("strategies.id"), nullable=False, index=True)
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False, index=True)

    # Screening details
    scan_timestamp = Column(DateTime(timezone=True), nullable=False)
    criteria_met = Column(JSON, nullable=False)  # Which criteria were met
    score = Column(Float, nullable=False)  # Overall screening score

    # Signal information
    signal_strength = Column(String(20), nullable=False)  # weak, moderate, strong
    entry_price = Column(Float)
    target_price = Column(Float)
    stop_loss_price = Column(Float)

    # Market data at time of screening
    market_data = Column(JSON)  # Snapshot of market data

    # Action taken
    action_taken = Column(String(20), default="none")  # none, order_placed, position_opened
    trade_id = Column(Integer, index=True)  # Reference to trade if action taken

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    strategy = relationship("Strategy", back_populates="screening_results")
    stock = relationship("Stock", back_populates="screening_results")

    # Indexes
    __table_args__ = (
        Index('idx_screening_result_strategy_timestamp', 'strategy_id', 'scan_timestamp'),
        Index('idx_screening_result_score', 'score'),
        Index('idx_screening_result_signal', 'signal_strength'),
    )

    def __repr__(self):
        return f"<ScreeningResult(id={self.id}, strategy_id={self.strategy_id}, stock_id={self.stock_id}, score={self.score})>"

    @property
    def is_strong_signal(self) -> bool:
        """Check if this is a strong signal"""
        return self.signal_strength == "strong"

    @property
    def potential_return(self) -> Optional[float]:
        """Calculate potential return percentage"""
        if self.entry_price and self.target_price:
            return ((self.target_price - self.entry_price) / self.entry_price) * 100
        return None

    @property
    def risk_reward_ratio(self) -> Optional[float]:
        """Calculate risk-reward ratio"""
        if self.entry_price and self.target_price and self.stop_loss_price:
            potential_gain = self.target_price - self.entry_price
            potential_loss = self.entry_price - self.stop_loss_price
            if potential_loss > 0:
                return potential_gain / potential_loss
        return None

    def to_dict(self) -> Dict[str, Any]:
        """Convert screening result to dictionary"""
        return {
            "id": self.id,
            "strategy_id": self.strategy_id,
            "stock_id": self.stock_id,
            "scan_timestamp": self.scan_timestamp.isoformat(),
            "criteria_met": self.criteria_met,
            "score": self.score,
            "signal_strength": self.signal_strength,
            "entry_price": self.entry_price,
            "target_price": self.target_price,
            "stop_loss_price": self.stop_loss_price,
            "market_data": self.market_data,
            "action_taken": self.action_taken,
            "trade_id": self.trade_id,
            "is_strong_signal": self.is_strong_signal,
            "potential_return": self.potential_return,
            "risk_reward_ratio": self.risk_reward_ratio,
            "created_at": self.created_at.isoformat(),
            "stock": self.stock.to_dict() if self.stock else None
        }
