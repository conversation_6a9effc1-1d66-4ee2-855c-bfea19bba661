"""
Subscription Management Service
Handles subscription tiers, limits, and upgrades
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.models.user import User
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


class SubscriptionManager:
    """Manages user subscriptions and tier-based features"""
    
    # Subscription tier configurations
    SUBSCRIPTION_TIERS = {
        "free": {
            "name": "Free",
            "price": 0,
            "features": {
                "max_portfolios": 2,
                "max_strategies": 5,
                "max_api_calls_per_day": 1000,
                "max_api_calls_per_hour": 100,
                "max_screening_results": 50,
                "max_backtests_per_day": 5,
                "real_time_data": False,
                "advanced_indicators": False,
                "paper_trading_only": True,
                "support_level": "community",
                "data_retention_days": 30
            }
        },
        "premium": {
            "name": "Premium",
            "price": 29.99,
            "features": {
                "max_portfolios": 10,
                "max_strategies": 25,
                "max_api_calls_per_day": 10000,
                "max_api_calls_per_hour": 1000,
                "max_screening_results": 500,
                "max_backtests_per_day": 50,
                "real_time_data": True,
                "advanced_indicators": True,
                "paper_trading_only": False,
                "support_level": "email",
                "data_retention_days": 365
            }
        },
        "enterprise": {
            "name": "Enterprise",
            "price": 99.99,
            "features": {
                "max_portfolios": 100,
                "max_strategies": 100,
                "max_api_calls_per_day": 100000,
                "max_api_calls_per_hour": 10000,
                "max_screening_results": 5000,
                "max_backtests_per_day": 500,
                "real_time_data": True,
                "advanced_indicators": True,
                "paper_trading_only": False,
                "support_level": "priority",
                "data_retention_days": 1095,
                "custom_integrations": True,
                "dedicated_support": True
            }
        }
    }
    
    @classmethod
    def get_tier_info(cls, tier: str) -> Dict[str, Any]:
        """Get information about a subscription tier"""
        return cls.SUBSCRIPTION_TIERS.get(tier, cls.SUBSCRIPTION_TIERS["free"])
    
    @classmethod
    def get_all_tiers(cls) -> Dict[str, Dict[str, Any]]:
        """Get all available subscription tiers"""
        return cls.SUBSCRIPTION_TIERS
    
    @classmethod
    def get_feature_limit(cls, tier: str, feature: str) -> Any:
        """Get specific feature limit for a tier"""
        tier_info = cls.get_tier_info(tier)
        return tier_info["features"].get(feature)
    
    @classmethod
    def can_access_feature(cls, tier: str, feature: str) -> bool:
        """Check if a tier can access a specific feature"""
        tier_info = cls.get_tier_info(tier)
        return tier_info["features"].get(feature, False)
    
    @classmethod
    def compare_tiers(cls, current_tier: str, target_tier: str) -> Dict[str, Any]:
        """Compare two subscription tiers"""
        current_info = cls.get_tier_info(current_tier)
        target_info = cls.get_tier_info(target_tier)
        
        tier_hierarchy = {"free": 0, "premium": 1, "enterprise": 2}
        
        return {
            "current_tier": current_info,
            "target_tier": target_info,
            "is_upgrade": tier_hierarchy.get(target_tier, 0) > tier_hierarchy.get(current_tier, 0),
            "price_difference": target_info["price"] - current_info["price"],
            "feature_improvements": cls._get_feature_improvements(current_info, target_info)
        }
    
    @classmethod
    def _get_feature_improvements(cls, current: Dict, target: Dict) -> Dict[str, Any]:
        """Get feature improvements when upgrading"""
        improvements = {}
        
        for feature, target_value in target["features"].items():
            current_value = current["features"].get(feature)
            
            if isinstance(target_value, (int, float)) and isinstance(current_value, (int, float)):
                if target_value > current_value:
                    improvements[feature] = {
                        "current": current_value,
                        "new": target_value,
                        "improvement": target_value - current_value
                    }
            elif isinstance(target_value, bool) and not current["features"].get(feature, False):
                if target_value:
                    improvements[feature] = {
                        "current": False,
                        "new": True,
                        "improvement": "Feature enabled"
                    }
        
        return improvements
    
    @classmethod
    async def check_usage_limits(cls, user: User, feature: str, current_usage: int) -> Dict[str, Any]:
        """Check if user is within usage limits for a feature"""
        tier_info = cls.get_tier_info(user.subscription_tier)
        limit = tier_info["features"].get(f"max_{feature}")
        
        if limit is None:
            return {"within_limit": True, "unlimited": True}
        
        within_limit = current_usage < limit
        usage_percentage = (current_usage / limit) * 100 if limit > 0 else 0
        
        return {
            "within_limit": within_limit,
            "current_usage": current_usage,
            "limit": limit,
            "remaining": max(0, limit - current_usage),
            "usage_percentage": usage_percentage,
            "tier": user.subscription_tier,
            "warning": usage_percentage >= 80,
            "critical": usage_percentage >= 95
        }
    
    @classmethod
    async def get_user_subscription_status(cls, user: User) -> Dict[str, Any]:
        """Get comprehensive subscription status for a user"""
        tier_info = cls.get_tier_info(user.subscription_tier)
        
        # Get current usage (this would typically come from database queries)
        current_usage = await cls._get_current_usage(user)
        
        # Check limits for various features
        limits_status = {}
        for feature in ["portfolios", "strategies", "api_calls_per_day"]:
            if f"max_{feature}" in tier_info["features"]:
                limits_status[feature] = await cls.check_usage_limits(
                    user, feature, current_usage.get(feature, 0)
                )
        
        return {
            "user_id": user.id,
            "current_tier": user.subscription_tier,
            "tier_info": tier_info,
            "limits_status": limits_status,
            "subscription_active": user.is_active,
            "can_upgrade": user.subscription_tier != "enterprise"
        }
    
    @classmethod
    async def _get_current_usage(cls, user: User) -> Dict[str, int]:
        """Get current usage statistics for a user"""
        # This would typically involve database queries
        # For now, return mock data based on relationships
        return {
            "portfolios": len(user.portfolios) if user.portfolios else 0,
            "strategies": len(user.strategies) if user.strategies else 0,
            "api_calls_per_day": 0,  # Would need to query from rate limiting logs
            "backtests_per_day": 0   # Would need to query from backtest logs
        }
    
    @classmethod
    async def suggest_upgrade(cls, user: User, required_feature: str, required_limit: int) -> Optional[str]:
        """Suggest appropriate tier upgrade based on requirements"""
        current_tier = user.subscription_tier
        
        for tier_name, tier_info in cls.SUBSCRIPTION_TIERS.items():
            if tier_name == current_tier:
                continue
                
            tier_limit = tier_info["features"].get(f"max_{required_feature}")
            if tier_limit and tier_limit >= required_limit:
                tier_hierarchy = {"free": 0, "premium": 1, "enterprise": 2}
                if tier_hierarchy.get(tier_name, 0) > tier_hierarchy.get(current_tier, 0):
                    return tier_name
        
        return None


# Dependency for checking subscription requirements
def require_subscription_tier(required_tier: str):
    """Dependency to require minimum subscription tier"""
    from fastapi import Depends
    from app.core.security import get_current_active_user

    def dependency(current_user: User = Depends(get_current_active_user)):
        tier_hierarchy = {"free": 0, "premium": 1, "enterprise": 2}
        user_tier_level = tier_hierarchy.get(current_user.subscription_tier, 0)
        required_tier_level = tier_hierarchy.get(required_tier, 0)

        if user_tier_level < required_tier_level:
            from fastapi import HTTPException, status
            tier_info = SubscriptionManager.get_tier_info(required_tier)
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "Insufficient subscription tier",
                    "message": f"This feature requires {tier_info['name']} subscription or higher",
                    "current_tier": current_user.subscription_tier,
                    "required_tier": required_tier,
                    "upgrade_url": f"/api/v1/subscription/upgrade/{required_tier}"
                }
            )

        return current_user

    return dependency
