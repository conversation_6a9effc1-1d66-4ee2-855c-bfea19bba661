import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  TextField,
  Button,
  Typography,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Divider,
  Alert,
  <PERSON>lider,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Add,
  Delete,
  ExpandMore,
  Save,
  Cancel,
  Help,
  TrendingUp,
  Assessment,
} from '@mui/icons-material';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store/store';

interface ScreeningCriteria {
  field: string;
  operator: string;
  value: number;
  weight: number;
}

interface StrategyFormData {
  name: string;
  description: string;
  screening_criteria: ScreeningCriteria[];
  universe: string[];
  min_score: number;
  max_results: number;
  is_active: boolean;
  schedule_enabled: boolean;
  schedule_frequency: string;
}

interface StrategyBuilderProps {
  strategy?: any;
  onSave: () => void;
  onCancel: () => void;
}

const StrategyBuilder: React.FC<StrategyBuilderProps> = ({
  strategy,
  onSave,
  onCancel,
}) => {
  const [availableFields, setAvailableFields] = useState<any>({});
  const [availableUniverses, setAvailableUniverses] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);

  const dispatch = useDispatch();
  const { user } = useSelector((state: RootState) => state.auth);

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
    setValue,
  } = useForm<StrategyFormData>({
    defaultValues: {
      name: strategy?.name || '',
      description: strategy?.description || '',
      screening_criteria: strategy?.screening_criteria || [
        { field: 'price', operator: '>', value: 10, weight: 1.0 },
      ],
      universe: strategy?.universe || ['SP500'],
      min_score: strategy?.min_score || 70,
      max_results: strategy?.max_results || 50,
      is_active: strategy?.is_active ?? true,
      schedule_enabled: strategy?.schedule_enabled || false,
      schedule_frequency: strategy?.schedule_frequency || 'daily',
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'screening_criteria',
  });

  const watchedCriteria = watch('screening_criteria');

  useEffect(() => {
    // Fetch available fields and universes
    fetchAvailableOptions();
  }, []);

  const fetchAvailableOptions = async () => {
    try {
      // Mock data - in real app, fetch from API
      setAvailableFields({
        price: {
          name: 'Price',
          type: 'number',
          description: 'Current stock price',
        },
        volume: {
          name: 'Volume',
          type: 'number',
          description: 'Trading volume',
        },
        market_cap: {
          name: 'Market Cap',
          type: 'number',
          description: 'Market capitalization',
        },
        rsi: {
          name: 'RSI',
          type: 'number',
          description: 'Relative Strength Index (0-100)',
        },
        sma_20: {
          name: 'SMA 20',
          type: 'number',
          description: '20-day Simple Moving Average',
        },
        sma_50: {
          name: 'SMA 50',
          type: 'number',
          description: '50-day Simple Moving Average',
        },
        sma_200: {
          name: 'SMA 200',
          type: 'number',
          description: '200-day Simple Moving Average',
        },
        pe_ratio: {
          name: 'P/E Ratio',
          type: 'number',
          description: 'Price-to-Earnings ratio',
        },
        pb_ratio: {
          name: 'P/B Ratio',
          type: 'number',
          description: 'Price-to-Book ratio',
        },
        debt_to_equity: {
          name: 'Debt/Equity',
          type: 'number',
          description: 'Debt-to-Equity ratio',
        },
      });

      setAvailableUniverses({
        SP500: {
          name: 'S&P 500',
          symbol_count: 500,
          subscription_required: 'free',
        },
        NASDAQ100: {
          name: 'NASDAQ 100',
          symbol_count: 100,
          subscription_required: 'free',
        },
        RUSSELL2000: {
          name: 'Russell 2000',
          symbol_count: 2000,
          subscription_required: 'premium',
        },
        ALL_US: {
          name: 'All US Stocks',
          symbol_count: 8000,
          subscription_required: 'enterprise',
        },
      });
    } catch (error) {
      console.error('Failed to fetch available options:', error);
    }
  };

  const onSubmit = async (data: StrategyFormData) => {
    setIsLoading(true);
    try {
      // dispatch(createStrategy(data) or updateStrategy(strategy.id, data));
      console.log('Strategy data:', data);
      onSave();
    } catch (error) {
      console.error('Failed to save strategy:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addCriteria = () => {
    append({ field: 'price', operator: '>', value: 0, weight: 1.0 });
  };

  const operators = [
    { value: '>', label: 'Greater than (>)' },
    { value: '<', label: 'Less than (<)' },
    { value: '>=', label: 'Greater than or equal (>=)' },
    { value: '<=', label: 'Less than or equal (<=)' },
    { value: '==', label: 'Equal to (==)' },
    { value: '!=', label: 'Not equal to (!=)' },
    { value: 'between', label: 'Between' },
  ];

  const scheduleOptions = [
    { value: 'hourly', label: 'Every Hour' },
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' },
  ];

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ mt: 2 }}>
      <Grid container spacing={3}>
        {/* Basic Information */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="name"
                    control={control}
                    rules={{ required: 'Strategy name is required' }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Strategy Name"
                        error={!!errors.name}
                        helperText={errors.name?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="universe"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth>
                        <InputLabel>Stock Universe</InputLabel>
                        <Select
                          {...field}
                          multiple
                          value={field.value}
                          renderValue={(selected) => (
                            <Box
                              sx={{
                                display: 'flex',
                                flexWrap: 'wrap',
                                gap: 0.5,
                              }}
                            >
                              {selected.map((value) => (
                                <Chip
                                  key={value}
                                  label={
                                    availableUniverses[value]?.name || value
                                  }
                                  size="small"
                                />
                              ))}
                            </Box>
                          )}
                        >
                          {Object.entries(availableUniverses).map(
                            ([key, universe]: [string, any]) => (
                              <MenuItem key={key} value={key}>
                                {universe.name} ({universe.symbol_count} stocks)
                              </MenuItem>
                            )
                          )}
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Controller
                    name="description"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        multiline
                        rows={3}
                        label="Description"
                        placeholder="Describe your screening strategy..."
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Screening Criteria */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: 2,
                }}
              >
                <Typography variant="h6">Screening Criteria</Typography>
                <Button
                  startIcon={<Add />}
                  onClick={addCriteria}
                  variant="outlined"
                  size="small"
                >
                  Add Criteria
                </Button>
              </Box>

              {fields.map((field, index) => (
                <Card key={field.id} variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Grid container spacing={2} alignItems="center">
                      <Grid item xs={12} sm={3}>
                        <Controller
                          name={`screening_criteria.${index}.field`}
                          control={control}
                          render={({ field }) => (
                            <FormControl fullWidth size="small">
                              <InputLabel>Field</InputLabel>
                              <Select {...field}>
                                {Object.entries(availableFields).map(
                                  ([key, fieldInfo]: [string, any]) => (
                                    <MenuItem key={key} value={key}>
                                      {fieldInfo.name}
                                    </MenuItem>
                                  )
                                )}
                              </Select>
                            </FormControl>
                          )}
                        />
                      </Grid>

                      <Grid item xs={12} sm={2}>
                        <Controller
                          name={`screening_criteria.${index}.operator`}
                          control={control}
                          render={({ field }) => (
                            <FormControl fullWidth size="small">
                              <InputLabel>Operator</InputLabel>
                              <Select {...field}>
                                {operators.map((op) => (
                                  <MenuItem key={op.value} value={op.value}>
                                    {op.label}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          )}
                        />
                      </Grid>

                      <Grid item xs={12} sm={2}>
                        <Controller
                          name={`screening_criteria.${index}.value`}
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              fullWidth
                              size="small"
                              label="Value"
                              type="number"
                              onChange={(e) =>
                                field.onChange(parseFloat(e.target.value))
                              }
                            />
                          )}
                        />
                      </Grid>

                      <Grid item xs={12} sm={2}>
                        <Controller
                          name={`screening_criteria.${index}.weight`}
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              fullWidth
                              size="small"
                              label="Weight"
                              type="number"
                              inputProps={{ min: 0.1, max: 10, step: 0.1 }}
                              onChange={(e) =>
                                field.onChange(parseFloat(e.target.value))
                              }
                            />
                          )}
                        />
                      </Grid>

                      <Grid item xs={12} sm={1}>
                        <IconButton
                          color="error"
                          onClick={() => remove(index)}
                          disabled={fields.length === 1}
                        >
                          <Delete />
                        </IconButton>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              ))}

              {fields.length === 0 && (
                <Alert severity="info">
                  Add at least one screening criteria to define your strategy.
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Advanced Settings */}
        <Grid item xs={12}>
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="h6">Advanced Settings</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography gutterBottom>Minimum Score</Typography>
                  <Controller
                    name="min_score"
                    control={control}
                    render={({ field }) => (
                      <Slider
                        {...field}
                        min={0}
                        max={100}
                        valueLabelDisplay="auto"
                        marks={[
                          { value: 0, label: '0%' },
                          { value: 50, label: '50%' },
                          { value: 100, label: '100%' },
                        ]}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="max_results"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Maximum Results"
                        type="number"
                        inputProps={{ min: 1, max: 500 }}
                        onChange={(e) =>
                          field.onChange(parseInt(e.target.value))
                        }
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="is_active"
                    control={control}
                    render={({ field }) => (
                      <FormControlLabel
                        control={<Switch {...field} checked={field.value} />}
                        label="Active Strategy"
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="schedule_enabled"
                    control={control}
                    render={({ field }) => (
                      <FormControlLabel
                        control={<Switch {...field} checked={field.value} />}
                        label="Enable Scheduled Screening"
                      />
                    )}
                  />
                </Grid>

                {watch('schedule_enabled') && (
                  <Grid item xs={12} md={6}>
                    <Controller
                      name="schedule_frequency"
                      control={control}
                      render={({ field }) => (
                        <FormControl fullWidth>
                          <InputLabel>Schedule Frequency</InputLabel>
                          <Select {...field}>
                            {scheduleOptions.map((option) => (
                              <MenuItem key={option.value} value={option.value}>
                                {option.label}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      )}
                    />
                  </Grid>
                )}
              </Grid>
            </AccordionDetails>
          </Accordion>
        </Grid>

        {/* Action Buttons */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button
              variant="outlined"
              onClick={onCancel}
              startIcon={<Cancel />}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              startIcon={<Save />}
              disabled={isLoading}
            >
              {strategy ? 'Update Strategy' : 'Create Strategy'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default StrategyBuilder;
