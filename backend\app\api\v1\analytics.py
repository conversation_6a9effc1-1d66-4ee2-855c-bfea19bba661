"""
Analytics API endpoints
"""

from fastapi import APIRouter, HTTPException, Depends, status, Query
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from app.core.security import get_current_active_user
from app.models.user import User
from app.services.analytics_service import analytics_service, EventType
from app.services.subscription_manager import require_subscription_tier
from app.utils.rate_limiter import api_rate_limiter

router = APIRouter()

# Pydantic models
class UsageStatsRequest(BaseModel):
    """Request model for usage statistics"""
    start_date: Optional[datetime] = Field(None, description="Start date for statistics")
    end_date: Optional[datetime] = Field(None, description="End date for statistics")
    granularity: str = Field("daily", description="Data granularity (hourly, daily)")

class AnalyticsResponse(BaseModel):
    """Analytics response model"""
    period: Dict[str, Any]
    data: Dict[str, Any]

class RealTimeMetricsResponse(BaseModel):
    """Real-time metrics response"""
    timestamp: str
    current_hour_stats: Dict[str, int]
    active_users: int
    system_health: str

@router.get("/usage-stats", response_model=AnalyticsResponse)
async def get_user_usage_stats(
    start_date: Optional[datetime] = Query(None, description="Start date"),
    end_date: Optional[datetime] = Query(None, description="End date"),
    granularity: str = Query("daily", description="Data granularity"),
    current_user: User = Depends(get_current_active_user)
):
    """Get usage statistics for current user"""
    # Apply rate limiting
    await api_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier,
        endpoint="analytics_usage"
    )
    
    stats = await analytics_service.get_usage_stats(
        user_id=current_user.id,
        start_date=start_date,
        end_date=end_date,
        granularity=granularity
    )
    
    return stats

@router.get("/tier-stats", response_model=AnalyticsResponse)
async def get_tier_usage_stats(
    tier: str = Query(..., description="Subscription tier"),
    start_date: Optional[datetime] = Query(None, description="Start date"),
    end_date: Optional[datetime] = Query(None, description="End date"),
    granularity: str = Query("daily", description="Data granularity"),
    current_user: User = Depends(require_subscription_tier("enterprise"))
):
    """Get usage statistics by subscription tier (Enterprise only)"""
    # Apply rate limiting
    await api_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier,
        endpoint="analytics_tier_stats"
    )
    
    stats = await analytics_service.get_usage_stats(
        subscription_tier=tier,
        start_date=start_date,
        end_date=end_date,
        granularity=granularity
    )
    
    return stats

@router.get("/system-stats", response_model=AnalyticsResponse)
async def get_system_usage_stats(
    start_date: Optional[datetime] = Query(None, description="Start date"),
    end_date: Optional[datetime] = Query(None, description="End date"),
    granularity: str = Query("daily", description="Data granularity"),
    current_user: User = Depends(require_subscription_tier("enterprise"))
):
    """Get system-wide usage statistics (Enterprise only)"""
    # Apply rate limiting
    await api_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier,
        endpoint="analytics_system_stats"
    )
    
    stats = await analytics_service.get_usage_stats(
        start_date=start_date,
        end_date=end_date,
        granularity=granularity
    )
    
    return stats

@router.get("/real-time", response_model=RealTimeMetricsResponse)
async def get_real_time_metrics(
    current_user: User = Depends(require_subscription_tier("premium"))
):
    """Get real-time system metrics (Premium+ only)"""
    # Apply rate limiting
    await api_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier,
        endpoint="analytics_realtime"
    )
    
    metrics = await analytics_service.get_real_time_metrics()
    return metrics

@router.post("/track-feature-usage")
async def track_feature_usage(
    feature_name: str = Query(..., description="Feature name"),
    usage_data: Optional[Dict[str, Any]] = None,
    current_user: User = Depends(get_current_active_user)
):
    """Track feature usage"""
    # Apply rate limiting
    await api_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier,
        endpoint="analytics_track_feature"
    )
    
    await analytics_service.track_feature_usage(
        feature_name=feature_name,
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier,
        usage_data=usage_data
    )
    
    return {
        "message": "Feature usage tracked successfully",
        "feature": feature_name,
        "timestamp": datetime.utcnow().isoformat()
    }

@router.get("/dashboard")
async def get_analytics_dashboard(
    current_user: User = Depends(get_current_active_user)
):
    """Get analytics dashboard data for current user"""
    # Apply rate limiting
    await api_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier,
        endpoint="analytics_dashboard"
    )
    
    # Get last 30 days of data
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=30)
    
    # Get user stats
    user_stats = await analytics_service.get_usage_stats(
        user_id=current_user.id,
        start_date=start_date,
        end_date=end_date,
        granularity="daily"
    )
    
    # Calculate summary metrics
    total_api_requests = 0
    total_feature_usage = 0
    error_count = 0
    
    for day_data in user_stats.get("data", {}).values():
        total_api_requests += day_data.get("api_request:count", 0)
        total_feature_usage += day_data.get("feature_usage:count", 0)
        error_count += day_data.get("error_occurred:count", 0)
    
    # Get subscription info
    from app.services.subscription_manager import SubscriptionManager
    subscription_status = await SubscriptionManager.get_user_subscription_status(current_user)
    
    dashboard_data = {
        "user_info": {
            "id": current_user.id,
            "subscription_tier": current_user.subscription_tier,
            "member_since": current_user.created_at.isoformat() if current_user.created_at else None
        },
        "usage_summary": {
            "period_days": 30,
            "total_api_requests": total_api_requests,
            "total_feature_usage": total_feature_usage,
            "error_count": error_count,
            "error_rate": (error_count / max(total_api_requests, 1)) * 100
        },
        "subscription_status": subscription_status,
        "usage_trends": user_stats,
        "generated_at": datetime.utcnow().isoformat()
    }
    
    return dashboard_data

@router.get("/export")
async def export_analytics_data(
    format: str = Query("json", description="Export format (json, csv)"),
    start_date: Optional[datetime] = Query(None, description="Start date"),
    end_date: Optional[datetime] = Query(None, description="End date"),
    current_user: User = Depends(require_subscription_tier("premium"))
):
    """Export analytics data (Premium+ only)"""
    # Apply rate limiting
    await api_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier,
        endpoint="analytics_export"
    )
    
    if format not in ["json", "csv"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid format. Supported formats: json, csv"
        )
    
    # Get user stats
    stats = await analytics_service.get_usage_stats(
        user_id=current_user.id,
        start_date=start_date,
        end_date=end_date,
        granularity="daily"
    )
    
    if format == "json":
        return {
            "export_format": "json",
            "exported_at": datetime.utcnow().isoformat(),
            "data": stats
        }
    else:  # CSV format
        # In a real implementation, you would convert to CSV format
        # For now, return a placeholder
        return {
            "export_format": "csv",
            "exported_at": datetime.utcnow().isoformat(),
            "message": "CSV export would be implemented here",
            "download_url": "/api/v1/analytics/download/csv/user_analytics.csv"
        }

@router.get("/health")
async def analytics_health_check():
    """Health check for analytics service"""
    try:
        # Test Redis connection
        from app.core.database import get_redis
        redis_client = await get_redis()
        await redis_client.ping()
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "services": {
                "redis": "connected",
                "analytics_service": "operational"
            }
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e),
            "services": {
                "redis": "disconnected",
                "analytics_service": "degraded"
            }
        }
