"""
User model for authentication and user management
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, JSON, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Optional, Dict, Any

from app.core.database import Base

class User(Base):
    """User model for authentication and profile management"""
    
    __tablename__ = "users"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Authentication
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(50), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    
    # Profile information
    first_name = Column(String(100))
    last_name = Column(String(100))
    phone = Column(String(20))
    
    # Account status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    is_admin = Column(Boolean, default=False)
    
    # API access
    api_key_hash = Column(String(255), unique=True, index=True)
    api_key_created_at = Column(DateTime(timezone=True))
    
    # Subscription and limits
    subscription_tier = Column(String(20), default="free")  # free, basic, premium
    max_portfolios = Column(Integer, default=1)
    max_strategies = Column(Integer, default=3)
    max_api_calls_per_day = Column(Integer, default=1000)
    
    # Trading preferences
    default_position_size = Column(Float, default=0.02)
    risk_tolerance = Column(String(20), default="medium")  # low, medium, high
    paper_trading_only = Column(Boolean, default=True)
    
    # Notification preferences
    email_notifications = Column(Boolean, default=True)
    sms_notifications = Column(Boolean, default=False)
    push_notifications = Column(Boolean, default=True)
    
    # Settings and preferences
    settings = Column(JSON, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))
    
    # Relationships
    portfolios = relationship("Portfolio", back_populates="user", cascade="all, delete-orphan")
    strategies = relationship("Strategy", back_populates="user", cascade="all, delete-orphan")
    trades = relationship("Trade", back_populates="user")
    broker_accounts = relationship("BrokerAccount", back_populates="user", cascade="all, delete-orphan")
    watchlists = relationship("Watchlist", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
    
    @property
    def full_name(self) -> str:
        """Get user's full name"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.username
    
    @property
    def is_premium(self) -> bool:
        """Check if user has premium subscription"""
        return self.subscription_tier in ["premium", "enterprise"]
    
    def can_create_portfolio(self) -> bool:
        """Check if user can create another portfolio"""
        return len(self.portfolios) < self.max_portfolios
    
    def can_create_strategy(self) -> bool:
        """Check if user can create another strategy"""
        return len(self.strategies) < self.max_strategies
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """Get user setting value"""
        if not self.settings:
            return default
        return self.settings.get(key, default)
    
    def set_setting(self, key: str, value: Any):
        """Set user setting value"""
        if not self.settings:
            self.settings = {}
        self.settings[key] = value
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert user to dictionary"""
        return {
            "id": self.id,
            "email": self.email,
            "username": self.username,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "full_name": self.full_name,
            "phone": self.phone,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "is_admin": self.is_admin,
            "subscription_tier": self.subscription_tier,
            "max_portfolios": self.max_portfolios,
            "max_strategies": self.max_strategies,
            "default_position_size": self.default_position_size,
            "risk_tolerance": self.risk_tolerance,
            "paper_trading_only": self.paper_trading_only,
            "email_notifications": self.email_notifications,
            "sms_notifications": self.sms_notifications,
            "push_notifications": self.push_notifications,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_login": self.last_login.isoformat() if self.last_login else None,
        }

class BrokerAccount(Base):
    """Broker account model for storing encrypted broker credentials"""
    
    __tablename__ = "broker_accounts"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign key
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # Broker information
    broker_name = Column(String(50), nullable=False)  # td_ameritrade, interactive_brokers, alpaca
    account_id = Column(String(100), nullable=False)
    account_name = Column(String(100))
    
    # Encrypted credentials
    encrypted_api_key = Column(Text)
    encrypted_secret_key = Column(Text)
    encrypted_access_token = Column(Text)
    encrypted_refresh_token = Column(Text)
    
    # Account status
    is_active = Column(Boolean, default=True)
    is_paper_trading = Column(Boolean, default=True)
    
    # Account limits and settings
    max_position_size = Column(Float, default=0.10)
    max_daily_loss = Column(Float, default=0.05)
    allowed_symbols = Column(JSON)  # List of allowed symbols, null = all allowed
    
    # Connection status
    last_connected = Column(DateTime(timezone=True))
    connection_status = Column(String(20), default="disconnected")  # connected, disconnected, error
    error_message = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="broker_accounts")
    trades = relationship("Trade", back_populates="broker_account")
    
    def __repr__(self):
        return f"<BrokerAccount(id={self.id}, broker='{self.broker_name}', account='{self.account_id}')>"
    
    @property
    def display_name(self) -> str:
        """Get display name for broker account"""
        return self.account_name or f"{self.broker_name.title()} - {self.account_id}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert broker account to dictionary (without sensitive data)"""
        return {
            "id": self.id,
            "broker_name": self.broker_name,
            "account_id": self.account_id,
            "account_name": self.account_name,
            "display_name": self.display_name,
            "is_active": self.is_active,
            "is_paper_trading": self.is_paper_trading,
            "max_position_size": self.max_position_size,
            "max_daily_loss": self.max_daily_loss,
            "connection_status": self.connection_status,
            "last_connected": self.last_connected.isoformat() if self.last_connected else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }

class UserSession(Base):
    """User session model for tracking active sessions"""
    
    __tablename__ = "user_sessions"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign key
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # Session information
    session_token = Column(String(255), unique=True, index=True, nullable=False)
    refresh_token = Column(String(255), unique=True, index=True)
    
    # Device and location information
    device_info = Column(JSON)
    ip_address = Column(String(45))
    user_agent = Column(Text)
    
    # Session status
    is_active = Column(Boolean, default=True)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_activity = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id}, active={self.is_active})>"
    
    @property
    def is_expired(self) -> bool:
        """Check if session is expired"""
        return datetime.utcnow() > self.expires_at
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert session to dictionary"""
        return {
            "id": self.id,
            "device_info": self.device_info,
            "ip_address": self.ip_address,
            "is_active": self.is_active,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "last_activity": self.last_activity.isoformat() if self.last_activity else None,
        }
