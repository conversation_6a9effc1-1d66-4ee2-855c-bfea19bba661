"""Fix foreign key relationships

Revision ID: de62fd9abbca
Revises: 0c009f72cb56
Create Date: 2025-07-13 14:13:42.321014

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'de62fd9abbca'
down_revision: Union[str, None] = '0c009f72cb56'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('broker_accounts', schema=None) as batch_op:
        batch_op.create_foreign_key(batch_op.f('fk_broker_accounts_user_id_users'), 'users', ['user_id'], ['id'])

    with op.batch_alter_table('positions', schema=None) as batch_op:
        batch_op.create_foreign_key(batch_op.f('fk_positions_user_id_users'), 'users', ['user_id'], ['id'])
        batch_op.create_foreign_key(batch_op.f('fk_positions_stock_id_stocks'), 'stocks', ['stock_id'], ['id'])
        batch_op.create_foreign_key(batch_op.f('fk_positions_portfolio_id_portfolios'), 'portfolios', ['portfolio_id'], ['id'])
        batch_op.create_foreign_key(batch_op.f('fk_positions_broker_account_id_broker_accounts'), 'broker_accounts', ['broker_account_id'], ['id'])

    with op.batch_alter_table('screening_results', schema=None) as batch_op:
        batch_op.create_foreign_key(batch_op.f('fk_screening_results_strategy_id_strategies'), 'strategies', ['strategy_id'], ['id'])
        batch_op.create_foreign_key(batch_op.f('fk_screening_results_stock_id_stocks'), 'stocks', ['stock_id'], ['id'])

    with op.batch_alter_table('trade_history', schema=None) as batch_op:
        batch_op.create_foreign_key(batch_op.f('fk_trade_history_strategy_id_strategies'), 'strategies', ['strategy_id'], ['id'])
        batch_op.create_foreign_key(batch_op.f('fk_trade_history_stock_id_stocks'), 'stocks', ['stock_id'], ['id'])
        batch_op.create_foreign_key(batch_op.f('fk_trade_history_user_id_users'), 'users', ['user_id'], ['id'])

    with op.batch_alter_table('trades', schema=None) as batch_op:
        batch_op.create_foreign_key(batch_op.f('fk_trades_stock_id_stocks'), 'stocks', ['stock_id'], ['id'])
        batch_op.create_foreign_key(batch_op.f('fk_trades_user_id_users'), 'users', ['user_id'], ['id'])
        batch_op.create_foreign_key(batch_op.f('fk_trades_broker_account_id_broker_accounts'), 'broker_accounts', ['broker_account_id'], ['id'])
        batch_op.create_foreign_key(batch_op.f('fk_trades_portfolio_id_portfolios'), 'portfolios', ['portfolio_id'], ['id'])
        batch_op.create_foreign_key(batch_op.f('fk_trades_strategy_id_strategies'), 'strategies', ['strategy_id'], ['id'])

    with op.batch_alter_table('user_sessions', schema=None) as batch_op:
        batch_op.create_foreign_key(batch_op.f('fk_user_sessions_user_id_users'), 'users', ['user_id'], ['id'])

    with op.batch_alter_table('watchlist_items', schema=None) as batch_op:
        batch_op.create_foreign_key(batch_op.f('fk_watchlist_items_stock_id_stocks'), 'stocks', ['stock_id'], ['id'])
        batch_op.create_foreign_key(batch_op.f('fk_watchlist_items_watchlist_id_watchlists'), 'watchlists', ['watchlist_id'], ['id'])

    with op.batch_alter_table('watchlists', schema=None) as batch_op:
        batch_op.create_foreign_key(batch_op.f('fk_watchlists_user_id_users'), 'users', ['user_id'], ['id'])

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('watchlists', schema=None) as batch_op:
        batch_op.drop_constraint(batch_op.f('fk_watchlists_user_id_users'), type_='foreignkey')

    with op.batch_alter_table('watchlist_items', schema=None) as batch_op:
        batch_op.drop_constraint(batch_op.f('fk_watchlist_items_watchlist_id_watchlists'), type_='foreignkey')
        batch_op.drop_constraint(batch_op.f('fk_watchlist_items_stock_id_stocks'), type_='foreignkey')

    with op.batch_alter_table('user_sessions', schema=None) as batch_op:
        batch_op.drop_constraint(batch_op.f('fk_user_sessions_user_id_users'), type_='foreignkey')

    with op.batch_alter_table('trades', schema=None) as batch_op:
        batch_op.drop_constraint(batch_op.f('fk_trades_strategy_id_strategies'), type_='foreignkey')
        batch_op.drop_constraint(batch_op.f('fk_trades_portfolio_id_portfolios'), type_='foreignkey')
        batch_op.drop_constraint(batch_op.f('fk_trades_broker_account_id_broker_accounts'), type_='foreignkey')
        batch_op.drop_constraint(batch_op.f('fk_trades_user_id_users'), type_='foreignkey')
        batch_op.drop_constraint(batch_op.f('fk_trades_stock_id_stocks'), type_='foreignkey')

    with op.batch_alter_table('trade_history', schema=None) as batch_op:
        batch_op.drop_constraint(batch_op.f('fk_trade_history_user_id_users'), type_='foreignkey')
        batch_op.drop_constraint(batch_op.f('fk_trade_history_stock_id_stocks'), type_='foreignkey')
        batch_op.drop_constraint(batch_op.f('fk_trade_history_strategy_id_strategies'), type_='foreignkey')

    with op.batch_alter_table('screening_results', schema=None) as batch_op:
        batch_op.drop_constraint(batch_op.f('fk_screening_results_stock_id_stocks'), type_='foreignkey')
        batch_op.drop_constraint(batch_op.f('fk_screening_results_strategy_id_strategies'), type_='foreignkey')

    with op.batch_alter_table('positions', schema=None) as batch_op:
        batch_op.drop_constraint(batch_op.f('fk_positions_broker_account_id_broker_accounts'), type_='foreignkey')
        batch_op.drop_constraint(batch_op.f('fk_positions_portfolio_id_portfolios'), type_='foreignkey')
        batch_op.drop_constraint(batch_op.f('fk_positions_stock_id_stocks'), type_='foreignkey')
        batch_op.drop_constraint(batch_op.f('fk_positions_user_id_users'), type_='foreignkey')

    with op.batch_alter_table('broker_accounts', schema=None) as batch_op:
        batch_op.drop_constraint(batch_op.f('fk_broker_accounts_user_id_users'), type_='foreignkey')

    # ### end Alembic commands ###
