"""
Advanced order management models for sophisticated trading strategies
"""

from sqlalchemy import <PERSON>umn, Integer, String, Float, DateTime, Boolean, Text, JSON, Enum, Index, ForeignKey, Numeric
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict, Any, Optional, List
from decimal import Decimal
import enum

from app.core.database import Base

class AdvancedOrderType(enum.Enum):
    """Advanced order types"""
    BRACKET = "bracket"
    OCO = "oco"  # One-Cancels-Other
    OTO = "oto"  # One-Triggers-Other
    TRAILING_STOP = "trailing_stop"
    CONDITIONAL = "conditional"
    ICEBERG = "iceberg"
    TWAP = "twap"  # Time-Weighted Average Price
    VWAP = "vwap"  # Volume-Weighted Average Price
    ALGO_MOMENTUM = "algo_momentum"
    ALGO_MEAN_REVERSION = "algo_mean_reversion"

class OrderConditionType(enum.Enum):
    """Order condition types"""
    PRICE_ABOVE = "price_above"
    PRICE_BELOW = "price_below"
    VOLUME_ABOVE = "volume_above"
    RSI_ABOVE = "rsi_above"
    RSI_BELOW = "rsi_below"
    MACD_CROSS_UP = "macd_cross_up"
    MACD_CROSS_DOWN = "macd_cross_down"
    TIME_BASED = "time_based"
    NEWS_SENTIMENT = "news_sentiment"

class OrderStatus(enum.Enum):
    """Advanced order status"""
    PENDING = "pending"
    ACTIVE = "active"
    TRIGGERED = "triggered"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"
    SUSPENDED = "suspended"

class BracketOrder(Base):
    """Bracket order model for entry with automatic stop-loss and take-profit"""
    
    __tablename__ = "bracket_orders"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False, index=True)
    broker_account_id = Column(Integer, ForeignKey("broker_accounts.id"), nullable=False, index=True)
    strategy_id = Column(Integer, ForeignKey("strategies.id"), index=True)
    
    # Parent order (entry order)
    parent_order_id = Column(Integer, ForeignKey("trades.id"), index=True)
    parent_symbol = Column(String(10), nullable=False)
    parent_quantity = Column(Integer, nullable=False)
    parent_order_type = Column(String(20), nullable=False)  # market, limit
    parent_side = Column(String(10), nullable=False)  # buy, sell
    parent_price = Column(Numeric(10, 4))  # For limit orders
    
    # Stop-loss order
    stop_loss_price = Column(Numeric(10, 4), nullable=False)
    stop_loss_order_id = Column(Integer, ForeignKey("trades.id"), index=True)
    stop_loss_type = Column(String(20), default="stop")  # stop, stop_limit
    stop_loss_limit_price = Column(Numeric(10, 4))  # For stop-limit orders
    
    # Take-profit order
    take_profit_price = Column(Numeric(10, 4), nullable=False)
    take_profit_order_id = Column(Integer, ForeignKey("trades.id"), index=True)
    take_profit_type = Column(String(20), default="limit")  # limit, market
    
    # Order management
    status = Column(Enum(OrderStatus), default=OrderStatus.PENDING)
    time_in_force = Column(String(10), default="gtc")  # day, gtc, ioc, fok
    
    # Risk management
    max_loss_amount = Column(Numeric(12, 2))
    max_profit_amount = Column(Numeric(12, 2))
    risk_reward_ratio = Column(Numeric(5, 2))
    
    # Execution tracking
    parent_filled_at = Column(DateTime(timezone=True))
    stop_loss_triggered_at = Column(DateTime(timezone=True))
    take_profit_triggered_at = Column(DateTime(timezone=True))
    
    # Performance
    realized_pnl = Column(Numeric(12, 2))
    commission_total = Column(Numeric(10, 2), default=0.0)
    
    # Paper trading flag
    is_paper_trade = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User")
    stock = relationship("Stock")
    broker_account = relationship("BrokerAccount")
    strategy = relationship("Strategy")
    parent_order = relationship("Trade", foreign_keys=[parent_order_id])
    stop_loss_order = relationship("Trade", foreign_keys=[stop_loss_order_id])
    take_profit_order = relationship("Trade", foreign_keys=[take_profit_order_id])
    
    # Indexes
    __table_args__ = (
        Index('idx_bracket_order_user', 'user_id'),
        Index('idx_bracket_order_status', 'status'),
        Index('idx_bracket_order_symbol', 'parent_symbol'),
    )
    
    def __repr__(self):
        return f"<BracketOrder(id={self.id}, symbol='{self.parent_symbol}', status='{self.status.value}')>"
    
    @property
    def is_active(self) -> bool:
        """Check if bracket order is active"""
        return self.status in [OrderStatus.PENDING, OrderStatus.ACTIVE, OrderStatus.TRIGGERED]
    
    @property
    def is_completed(self) -> bool:
        """Check if bracket order is completed"""
        return self.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.EXPIRED]
    
    @property
    def entry_price(self) -> Optional[float]:
        """Get entry price from parent order"""
        if self.parent_order and self.parent_order.average_fill_price:
            return float(self.parent_order.average_fill_price)
        return float(self.parent_price) if self.parent_price else None
    
    @property
    def stop_distance(self) -> Optional[float]:
        """Calculate stop-loss distance from entry"""
        entry = self.entry_price
        if entry and self.stop_loss_price:
            return abs(entry - float(self.stop_loss_price))
        return None
    
    @property
    def profit_target_distance(self) -> Optional[float]:
        """Calculate take-profit distance from entry"""
        entry = self.entry_price
        if entry and self.take_profit_price:
            return abs(float(self.take_profit_price) - entry)
        return None
    
    def calculate_risk_reward(self) -> Optional[float]:
        """Calculate risk-reward ratio"""
        stop_dist = self.stop_distance
        profit_dist = self.profit_target_distance
        
        if stop_dist and profit_dist and stop_dist > 0:
            return profit_dist / stop_dist
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert bracket order to dictionary"""
        return {
            "id": self.id,
            "parent_symbol": self.parent_symbol,
            "parent_quantity": self.parent_quantity,
            "parent_order_type": self.parent_order_type,
            "parent_side": self.parent_side,
            "parent_price": float(self.parent_price) if self.parent_price else None,
            "stop_loss_price": float(self.stop_loss_price) if self.stop_loss_price else None,
            "take_profit_price": float(self.take_profit_price) if self.take_profit_price else None,
            "status": self.status.value,
            "entry_price": self.entry_price,
            "stop_distance": self.stop_distance,
            "profit_target_distance": self.profit_target_distance,
            "risk_reward_ratio": self.calculate_risk_reward(),
            "max_loss_amount": float(self.max_loss_amount) if self.max_loss_amount else None,
            "max_profit_amount": float(self.max_profit_amount) if self.max_profit_amount else None,
            "realized_pnl": float(self.realized_pnl) if self.realized_pnl else None,
            "is_active": self.is_active,
            "is_completed": self.is_completed,
            "is_paper_trade": self.is_paper_trade,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }


class OCOOrder(Base):
    """One-Cancels-Other order model"""

    __tablename__ = "oco_orders"

    # Primary key
    id = Column(Integer, primary_key=True, index=True)

    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False, index=True)
    broker_account_id = Column(Integer, ForeignKey("broker_accounts.id"), nullable=False, index=True)
    strategy_id = Column(Integer, ForeignKey("strategies.id"), index=True)

    # OCO group information
    oco_group_id = Column(String(50), nullable=False, index=True)  # Unique group identifier

    # Primary order
    primary_order_id = Column(Integer, ForeignKey("trades.id"), nullable=False, index=True)
    primary_order_type = Column(String(20), nullable=False)
    primary_price = Column(Numeric(10, 4))
    primary_quantity = Column(Integer, nullable=False)
    primary_side = Column(String(10), nullable=False)

    # Secondary order
    secondary_order_id = Column(Integer, ForeignKey("trades.id"), nullable=False, index=True)
    secondary_order_type = Column(String(20), nullable=False)
    secondary_price = Column(Numeric(10, 4))
    secondary_quantity = Column(Integer, nullable=False)
    secondary_side = Column(String(10), nullable=False)

    # Order management
    status = Column(Enum(OrderStatus), default=OrderStatus.PENDING)
    time_in_force = Column(String(10), default="gtc")

    # Execution tracking
    triggered_order_id = Column(Integer)  # Which order was triggered
    cancelled_order_id = Column(Integer)  # Which order was cancelled
    triggered_at = Column(DateTime(timezone=True))
    cancelled_at = Column(DateTime(timezone=True))

    # Paper trading flag
    is_paper_trade = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User")
    stock = relationship("Stock")
    broker_account = relationship("BrokerAccount")
    strategy = relationship("Strategy")
    primary_order = relationship("Trade", foreign_keys=[primary_order_id])
    secondary_order = relationship("Trade", foreign_keys=[secondary_order_id])

    # Indexes
    __table_args__ = (
        Index('idx_oco_order_user', 'user_id'),
        Index('idx_oco_order_group', 'oco_group_id'),
        Index('idx_oco_order_status', 'status'),
    )

    def __repr__(self):
        return f"<OCOOrder(id={self.id}, group='{self.oco_group_id}', status='{self.status.value}')>"

    @property
    def is_active(self) -> bool:
        """Check if OCO order is active"""
        return self.status in [OrderStatus.PENDING, OrderStatus.ACTIVE]

    @property
    def is_triggered(self) -> bool:
        """Check if one of the orders was triggered"""
        return self.status == OrderStatus.TRIGGERED

    def to_dict(self) -> Dict[str, Any]:
        """Convert OCO order to dictionary"""
        return {
            "id": self.id,
            "oco_group_id": self.oco_group_id,
            "primary_order_type": self.primary_order_type,
            "primary_price": float(self.primary_price) if self.primary_price else None,
            "primary_quantity": self.primary_quantity,
            "primary_side": self.primary_side,
            "secondary_order_type": self.secondary_order_type,
            "secondary_price": float(self.secondary_price) if self.secondary_price else None,
            "secondary_quantity": self.secondary_quantity,
            "secondary_side": self.secondary_side,
            "status": self.status.value,
            "triggered_order_id": self.triggered_order_id,
            "cancelled_order_id": self.cancelled_order_id,
            "is_active": self.is_active,
            "is_triggered": self.is_triggered,
            "is_paper_trade": self.is_paper_trade,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }


class ConditionalOrder(Base):
    """Conditional order model with custom triggers"""

    __tablename__ = "conditional_orders"

    # Primary key
    id = Column(Integer, primary_key=True, index=True)

    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False, index=True)
    broker_account_id = Column(Integer, ForeignKey("broker_accounts.id"), nullable=False, index=True)
    strategy_id = Column(Integer, ForeignKey("strategies.id"), index=True)

    # Order details
    symbol = Column(String(10), nullable=False)
    order_type = Column(String(20), nullable=False)  # market, limit, stop
    order_side = Column(String(10), nullable=False)  # buy, sell
    quantity = Column(Integer, nullable=False)
    price = Column(Numeric(10, 4))  # For limit orders

    # Condition configuration
    condition_type = Column(Enum(OrderConditionType), nullable=False)
    condition_value = Column(Numeric(15, 6), nullable=False)  # Trigger value
    condition_operator = Column(String(10), nullable=False)  # >=, <=, ==, !=
    condition_symbol = Column(String(10))  # Symbol to monitor (can be different from order symbol)

    # Advanced conditions
    condition_timeframe = Column(String(10), default="1m")  # 1m, 5m, 15m, 1h, 1d
    condition_lookback = Column(Integer, default=1)  # Number of periods to look back
    condition_logic = Column(JSON)  # Complex condition logic

    # Order management
    status = Column(Enum(OrderStatus), default=OrderStatus.PENDING)
    time_in_force = Column(String(10), default="gtc")
    expiration_time = Column(DateTime(timezone=True))

    # Execution tracking
    condition_met_at = Column(DateTime(timezone=True))
    order_submitted_at = Column(DateTime(timezone=True))
    triggered_order_id = Column(Integer, ForeignKey("trades.id"), index=True)

    # Monitoring data
    last_check_time = Column(DateTime(timezone=True))
    last_check_value = Column(Numeric(15, 6))
    check_count = Column(Integer, default=0)

    # Paper trading flag
    is_paper_trade = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User")
    stock = relationship("Stock")
    broker_account = relationship("BrokerAccount")
    strategy = relationship("Strategy")
    triggered_order = relationship("Trade", foreign_keys=[triggered_order_id])

    # Indexes
    __table_args__ = (
        Index('idx_conditional_order_user', 'user_id'),
        Index('idx_conditional_order_status', 'status'),
        Index('idx_conditional_order_condition', 'condition_type', 'condition_symbol'),
    )

    def __repr__(self):
        return f"<ConditionalOrder(id={self.id}, symbol='{self.symbol}', condition='{self.condition_type.value}')>"

    @property
    def is_active(self) -> bool:
        """Check if conditional order is active"""
        return self.status in [OrderStatus.PENDING, OrderStatus.ACTIVE]

    @property
    def is_expired(self) -> bool:
        """Check if conditional order has expired"""
        if self.expiration_time:
            return datetime.utcnow() > self.expiration_time.replace(tzinfo=None)
        return False

    def check_condition(self, current_value: float) -> bool:
        """Check if condition is met"""
        condition_val = float(self.condition_value)

        if self.condition_operator == ">=":
            return current_value >= condition_val
        elif self.condition_operator == "<=":
            return current_value <= condition_val
        elif self.condition_operator == "==":
            return abs(current_value - condition_val) < 0.001  # Small tolerance for floats
        elif self.condition_operator == "!=":
            return abs(current_value - condition_val) >= 0.001
        elif self.condition_operator == ">":
            return current_value > condition_val
        elif self.condition_operator == "<":
            return current_value < condition_val

        return False

    def to_dict(self) -> Dict[str, Any]:
        """Convert conditional order to dictionary"""
        return {
            "id": self.id,
            "symbol": self.symbol,
            "order_type": self.order_type,
            "order_side": self.order_side,
            "quantity": self.quantity,
            "price": float(self.price) if self.price else None,
            "condition_type": self.condition_type.value,
            "condition_value": float(self.condition_value) if self.condition_value else None,
            "condition_operator": self.condition_operator,
            "condition_symbol": self.condition_symbol,
            "condition_timeframe": self.condition_timeframe,
            "status": self.status.value,
            "last_check_value": float(self.last_check_value) if self.last_check_value else None,
            "check_count": self.check_count,
            "is_active": self.is_active,
            "is_expired": self.is_expired,
            "is_paper_trade": self.is_paper_trade,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "condition_met_at": self.condition_met_at.isoformat() if self.condition_met_at else None,
        }


class TrailingStopOrder(Base):
    """Trailing stop order model with dynamic stop adjustment"""

    __tablename__ = "trailing_stop_orders"

    # Primary key
    id = Column(Integer, primary_key=True, index=True)

    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False, index=True)
    broker_account_id = Column(Integer, ForeignKey("broker_accounts.id"), nullable=False, index=True)
    position_id = Column(Integer, ForeignKey("positions.id"), nullable=False, index=True)

    # Order details
    symbol = Column(String(10), nullable=False)
    quantity = Column(Integer, nullable=False)
    order_side = Column(String(10), nullable=False)  # sell (for long positions), buy (for short positions)

    # Trailing stop configuration
    trail_type = Column(String(20), nullable=False)  # percent, amount
    trail_value = Column(Numeric(10, 4), nullable=False)  # Percentage or dollar amount

    # Current tracking
    current_stop_price = Column(Numeric(10, 4), nullable=False)
    highest_price = Column(Numeric(10, 4))  # For long positions
    lowest_price = Column(Numeric(10, 4))   # For short positions

    # Initial values
    initial_stop_price = Column(Numeric(10, 4), nullable=False)
    initial_price = Column(Numeric(10, 4), nullable=False)

    # Order management
    status = Column(Enum(OrderStatus), default=OrderStatus.ACTIVE)
    time_in_force = Column(String(10), default="gtc")

    # Execution tracking
    triggered_at = Column(DateTime(timezone=True))
    triggered_price = Column(Numeric(10, 4))
    executed_order_id = Column(Integer, ForeignKey("trades.id"), index=True)

    # Performance tracking
    last_update_time = Column(DateTime(timezone=True))
    last_price_check = Column(Numeric(10, 4))
    adjustment_count = Column(Integer, default=0)

    # Paper trading flag
    is_paper_trade = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User")
    stock = relationship("Stock")
    broker_account = relationship("BrokerAccount")
    position = relationship("Position")
    executed_order = relationship("Trade", foreign_keys=[executed_order_id])

    # Indexes
    __table_args__ = (
        Index('idx_trailing_stop_user', 'user_id'),
        Index('idx_trailing_stop_position', 'position_id'),
        Index('idx_trailing_stop_status', 'status'),
        Index('idx_trailing_stop_symbol', 'symbol'),
    )

    def __repr__(self):
        return f"<TrailingStopOrder(id={self.id}, symbol='{self.symbol}', stop_price={self.current_stop_price})>"

    @property
    def is_active(self) -> bool:
        """Check if trailing stop is active"""
        return self.status == OrderStatus.ACTIVE

    @property
    def is_triggered(self) -> bool:
        """Check if trailing stop was triggered"""
        return self.status == OrderStatus.TRIGGERED

    @property
    def is_long_position(self) -> bool:
        """Check if this is for a long position"""
        return self.order_side == "sell"

    @property
    def is_short_position(self) -> bool:
        """Check if this is for a short position"""
        return self.order_side == "buy"

    def update_stop_price(self, current_price: float) -> bool:
        """Update trailing stop price based on current market price"""
        current_price_decimal = Decimal(str(current_price))
        trail_value_decimal = self.trail_value
        updated = False

        if self.is_long_position:
            # For long positions, trail up with the price
            if self.highest_price is None or current_price_decimal > self.highest_price:
                self.highest_price = current_price_decimal

                # Calculate new stop price
                if self.trail_type == "percent":
                    new_stop = current_price_decimal * (1 - trail_value_decimal / 100)
                else:  # amount
                    new_stop = current_price_decimal - trail_value_decimal

                # Only update if new stop is higher than current stop
                if new_stop > self.current_stop_price:
                    self.current_stop_price = new_stop
                    self.adjustment_count += 1
                    updated = True

        else:  # Short position
            # For short positions, trail down with the price
            if self.lowest_price is None or current_price_decimal < self.lowest_price:
                self.lowest_price = current_price_decimal

                # Calculate new stop price
                if self.trail_type == "percent":
                    new_stop = current_price_decimal * (1 + trail_value_decimal / 100)
                else:  # amount
                    new_stop = current_price_decimal + trail_value_decimal

                # Only update if new stop is lower than current stop
                if new_stop < self.current_stop_price:
                    self.current_stop_price = new_stop
                    self.adjustment_count += 1
                    updated = True

        self.last_price_check = current_price_decimal
        self.last_update_time = datetime.utcnow()

        return updated

    def check_trigger(self, current_price: float) -> bool:
        """Check if trailing stop should be triggered"""
        if not self.is_active:
            return False

        current_price_decimal = Decimal(str(current_price))

        if self.is_long_position:
            # Trigger if price falls below stop price
            return current_price_decimal <= self.current_stop_price
        else:
            # Trigger if price rises above stop price
            return current_price_decimal >= self.current_stop_price

    def to_dict(self) -> Dict[str, Any]:
        """Convert trailing stop order to dictionary"""
        return {
            "id": self.id,
            "symbol": self.symbol,
            "quantity": self.quantity,
            "order_side": self.order_side,
            "trail_type": self.trail_type,
            "trail_value": float(self.trail_value) if self.trail_value else None,
            "current_stop_price": float(self.current_stop_price) if self.current_stop_price else None,
            "initial_stop_price": float(self.initial_stop_price) if self.initial_stop_price else None,
            "initial_price": float(self.initial_price) if self.initial_price else None,
            "highest_price": float(self.highest_price) if self.highest_price else None,
            "lowest_price": float(self.lowest_price) if self.lowest_price else None,
            "status": self.status.value,
            "adjustment_count": self.adjustment_count,
            "last_price_check": float(self.last_price_check) if self.last_price_check else None,
            "is_active": self.is_active,
            "is_triggered": self.is_triggered,
            "is_long_position": self.is_long_position,
            "is_short_position": self.is_short_position,
            "is_paper_trade": self.is_paper_trade,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "triggered_at": self.triggered_at.isoformat() if self.triggered_at else None,
        }
