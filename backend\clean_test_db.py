#!/usr/bin/env python3
"""
Clean test database script
"""

import asyncio
import os
from sqlalchemy import text
from app.core.database import get_db, init_db

async def clean_database():
    """Clean all test data from database"""
    print("🧹 Cleaning test database...")
    
    await init_db()
    
    async with get_db() as db:
        # Delete all data from tables in correct order (respecting foreign keys)
        tables_to_clean = [
            "screening_results",
            "watchlist_items", 
            "watchlists",
            "trade_history",
            "positions",
            "trades",
            "user_sessions",
            "broker_accounts",
            "market_data",
            "stocks",
            "strategies",
            "portfolios",
            "users"
        ]
        
        for table in tables_to_clean:
            try:
                await db.execute(text(f"DELETE FROM {table}"))
                print(f"✅ Cleaned {table}")
            except Exception as e:
                print(f"⚠️  Warning cleaning {table}: {e}")
        
        await db.commit()
        print("✅ Database cleaned successfully!")

if __name__ == "__main__":
    asyncio.run(clean_database())
