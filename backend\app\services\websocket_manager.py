"""
WebSocket Manager for Real-time Data Streaming
Handles WebSocket connections, subscriptions, and real-time data distribution
"""

import asyncio
import json
import logging
from typing import Dict, Set, List, Any, Optional, Callable
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
import weakref

from fastapi import WebSocket, WebSocketDisconnect
from app.core.database import get_redis
from app.services.analytics_service import analytics_service, EventType

logger = logging.getLogger(__name__)


class MessageType(str, Enum):
    """WebSocket message types"""
    SUBSCRIBE = "subscribe"
    UNSUBSCRIBE = "unsubscribe"
    QUOTE_UPDATE = "quote_update"
    TRADE_UPDATE = "trade_update"
    ORDER_UPDATE = "order_update"
    PORTFOLIO_UPDATE = "portfolio_update"
    ALERT = "alert"
    ERROR = "error"
    HEARTBEAT = "heartbeat"
    MARKET_STATUS = "market_status"


@dataclass
class WebSocketMessage:
    """WebSocket message structure"""
    type: MessageType
    data: Dict[str, Any]
    timestamp: str = None
    user_id: Optional[int] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow().isoformat()


class ConnectionManager:
    """Manages WebSocket connections and subscriptions"""
    
    def __init__(self):
        # Active connections: user_id -> WebSocket
        self.active_connections: Dict[int, WebSocket] = {}
        
        # Subscriptions: symbol -> set of user_ids
        self.symbol_subscriptions: Dict[str, Set[int]] = {}
        
        # User subscriptions: user_id -> set of symbols
        self.user_subscriptions: Dict[int, Set[str]] = {}
        
        # Connection metadata
        self.connection_metadata: Dict[int, Dict[str, Any]] = {}
        
        # Message handlers
        self.message_handlers: Dict[MessageType, Callable] = {
            MessageType.SUBSCRIBE: self._handle_subscribe,
            MessageType.UNSUBSCRIBE: self._handle_unsubscribe,
            MessageType.HEARTBEAT: self._handle_heartbeat
        }
        
        # Background tasks
        self._heartbeat_task = None
        self._cleanup_task = None
        
    async def start(self):
        """Start the connection manager"""
        self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info("WebSocket connection manager started")
    
    async def stop(self):
        """Stop the connection manager"""
        if self._heartbeat_task:
            self._heartbeat_task.cancel()
        if self._cleanup_task:
            self._cleanup_task.cancel()
        
        # Close all connections
        for websocket in self.active_connections.values():
            try:
                await websocket.close()
            except Exception:
                pass
        
        self.active_connections.clear()
        self.symbol_subscriptions.clear()
        self.user_subscriptions.clear()
        
        logger.info("WebSocket connection manager stopped")
    
    async def connect(self, websocket: WebSocket, user_id: int, subscription_tier: str):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        
        # Store connection
        self.active_connections[user_id] = websocket
        self.user_subscriptions[user_id] = set()
        self.connection_metadata[user_id] = {
            "connected_at": datetime.utcnow().isoformat(),
            "subscription_tier": subscription_tier,
            "last_heartbeat": datetime.utcnow().isoformat(),
            "message_count": 0
        }
        
        # Track connection analytics
        await analytics_service.track_event(
            event_type=EventType.USER_LOGIN,
            user_id=user_id,
            data={"connection_type": "websocket"},
            subscription_tier=subscription_tier
        )
        
        logger.info(f"WebSocket connected for user {user_id}")
        
        # Send welcome message
        welcome_msg = WebSocketMessage(
            type=MessageType.MARKET_STATUS,
            data={
                "status": "connected",
                "message": "Welcome to MarketHawk real-time data",
                "subscription_tier": subscription_tier,
                "features": self._get_tier_features(subscription_tier)
            },
            user_id=user_id
        )
        await self._send_to_user(user_id, welcome_msg)
    
    async def disconnect(self, user_id: int):
        """Handle WebSocket disconnection"""
        if user_id in self.active_connections:
            # Remove from all symbol subscriptions
            user_symbols = self.user_subscriptions.get(user_id, set())
            for symbol in user_symbols:
                if symbol in self.symbol_subscriptions:
                    self.symbol_subscriptions[symbol].discard(user_id)
                    if not self.symbol_subscriptions[symbol]:
                        del self.symbol_subscriptions[symbol]
            
            # Clean up user data
            del self.active_connections[user_id]
            del self.user_subscriptions[user_id]
            del self.connection_metadata[user_id]
            
            # Track disconnection analytics
            await analytics_service.track_event(
                event_type=EventType.USER_LOGOUT,
                user_id=user_id,
                data={"connection_type": "websocket"}
            )
            
            logger.info(f"WebSocket disconnected for user {user_id}")
    
    async def handle_message(self, user_id: int, message: str):
        """Handle incoming WebSocket message"""
        try:
            data = json.loads(message)
            msg_type = MessageType(data.get("type"))
            
            # Update message count
            if user_id in self.connection_metadata:
                self.connection_metadata[user_id]["message_count"] += 1
            
            # Handle message based on type
            handler = self.message_handlers.get(msg_type)
            if handler:
                await handler(user_id, data.get("data", {}))
            else:
                await self._send_error(user_id, f"Unknown message type: {msg_type}")
                
        except json.JSONDecodeError:
            await self._send_error(user_id, "Invalid JSON message")
        except ValueError as e:
            await self._send_error(user_id, f"Invalid message type: {e}")
        except Exception as e:
            logger.error(f"Error handling WebSocket message from user {user_id}: {e}")
            await self._send_error(user_id, "Internal server error")
    
    async def broadcast_quote_update(self, symbol: str, quote_data: Dict[str, Any]):
        """Broadcast quote update to all subscribers"""
        if symbol not in self.symbol_subscriptions:
            return
        
        message = WebSocketMessage(
            type=MessageType.QUOTE_UPDATE,
            data={
                "symbol": symbol,
                "quote": quote_data
            }
        )
        
        # Send to all subscribers
        subscribers = self.symbol_subscriptions[symbol].copy()
        for user_id in subscribers:
            await self._send_to_user(user_id, message)
    
    async def send_portfolio_update(self, user_id: int, portfolio_data: Dict[str, Any]):
        """Send portfolio update to specific user"""
        message = WebSocketMessage(
            type=MessageType.PORTFOLIO_UPDATE,
            data=portfolio_data,
            user_id=user_id
        )
        await self._send_to_user(user_id, message)
    
    async def send_order_update(self, user_id: int, order_data: Dict[str, Any]):
        """Send order update to specific user"""
        message = WebSocketMessage(
            type=MessageType.ORDER_UPDATE,
            data=order_data,
            user_id=user_id
        )
        await self._send_to_user(user_id, message)
    
    async def send_alert(self, user_id: int, alert_data: Dict[str, Any]):
        """Send alert to specific user"""
        message = WebSocketMessage(
            type=MessageType.ALERT,
            data=alert_data,
            user_id=user_id
        )
        await self._send_to_user(user_id, message)
    
    async def _handle_subscribe(self, user_id: int, data: Dict[str, Any]):
        """Handle subscription request"""
        symbols = data.get("symbols", [])
        if not isinstance(symbols, list):
            symbols = [symbols]
        
        # Check subscription limits based on tier
        subscription_tier = self.connection_metadata.get(user_id, {}).get("subscription_tier", "free")
        max_subscriptions = self._get_max_subscriptions(subscription_tier)
        
        current_subscriptions = len(self.user_subscriptions.get(user_id, set()))
        if current_subscriptions + len(symbols) > max_subscriptions:
            await self._send_error(
                user_id, 
                f"Subscription limit exceeded. Maximum {max_subscriptions} symbols allowed for {subscription_tier} tier"
            )
            return
        
        # Add subscriptions
        for symbol in symbols:
            symbol = symbol.upper()
            
            # Add to user subscriptions
            self.user_subscriptions[user_id].add(symbol)
            
            # Add to symbol subscriptions
            if symbol not in self.symbol_subscriptions:
                self.symbol_subscriptions[symbol] = set()
            self.symbol_subscriptions[symbol].add(user_id)
        
        # Send confirmation
        await self._send_to_user(user_id, WebSocketMessage(
            type=MessageType.SUBSCRIBE,
            data={
                "status": "success",
                "symbols": symbols,
                "message": f"Subscribed to {len(symbols)} symbols"
            },
            user_id=user_id
        ))
        
        logger.info(f"User {user_id} subscribed to symbols: {symbols}")
    
    async def _handle_unsubscribe(self, user_id: int, data: Dict[str, Any]):
        """Handle unsubscription request"""
        symbols = data.get("symbols", [])
        if not isinstance(symbols, list):
            symbols = [symbols]
        
        # Remove subscriptions
        for symbol in symbols:
            symbol = symbol.upper()
            
            # Remove from user subscriptions
            self.user_subscriptions[user_id].discard(symbol)
            
            # Remove from symbol subscriptions
            if symbol in self.symbol_subscriptions:
                self.symbol_subscriptions[symbol].discard(user_id)
                if not self.symbol_subscriptions[symbol]:
                    del self.symbol_subscriptions[symbol]
        
        # Send confirmation
        await self._send_to_user(user_id, WebSocketMessage(
            type=MessageType.UNSUBSCRIBE,
            data={
                "status": "success",
                "symbols": symbols,
                "message": f"Unsubscribed from {len(symbols)} symbols"
            },
            user_id=user_id
        ))
        
        logger.info(f"User {user_id} unsubscribed from symbols: {symbols}")
    
    async def _handle_heartbeat(self, user_id: int, data: Dict[str, Any]):
        """Handle heartbeat message"""
        if user_id in self.connection_metadata:
            self.connection_metadata[user_id]["last_heartbeat"] = datetime.utcnow().isoformat()
        
        # Send heartbeat response
        await self._send_to_user(user_id, WebSocketMessage(
            type=MessageType.HEARTBEAT,
            data={"status": "alive", "server_time": datetime.utcnow().isoformat()},
            user_id=user_id
        ))
    
    async def _send_to_user(self, user_id: int, message: WebSocketMessage):
        """Send message to specific user"""
        if user_id not in self.active_connections:
            return
        
        try:
            websocket = self.active_connections[user_id]
            await websocket.send_text(json.dumps(asdict(message)))
        except Exception as e:
            logger.error(f"Error sending message to user {user_id}: {e}")
            # Remove disconnected user
            await self.disconnect(user_id)
    
    async def _send_error(self, user_id: int, error_message: str):
        """Send error message to user"""
        error_msg = WebSocketMessage(
            type=MessageType.ERROR,
            data={"error": error_message},
            user_id=user_id
        )
        await self._send_to_user(user_id, error_msg)
    
    async def _heartbeat_loop(self):
        """Send periodic heartbeat to all connections"""
        while True:
            try:
                await asyncio.sleep(30)  # Send heartbeat every 30 seconds
                
                current_time = datetime.utcnow().isoformat()
                heartbeat_msg = WebSocketMessage(
                    type=MessageType.HEARTBEAT,
                    data={"server_time": current_time}
                )
                
                # Send to all active connections
                for user_id in list(self.active_connections.keys()):
                    await self._send_to_user(user_id, heartbeat_msg)
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in heartbeat loop: {e}")
    
    async def _cleanup_loop(self):
        """Clean up stale connections"""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                current_time = datetime.utcnow()
                stale_users = []
                
                for user_id, metadata in self.connection_metadata.items():
                    last_heartbeat = datetime.fromisoformat(metadata["last_heartbeat"])
                    if (current_time - last_heartbeat).total_seconds() > 120:  # 2 minutes timeout
                        stale_users.append(user_id)
                
                # Remove stale connections
                for user_id in stale_users:
                    logger.info(f"Removing stale WebSocket connection for user {user_id}")
                    await self.disconnect(user_id)
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
    
    def _get_tier_features(self, subscription_tier: str) -> Dict[str, Any]:
        """Get WebSocket features for subscription tier"""
        features = {
            "free": {
                "max_subscriptions": 5,
                "update_frequency": "5s",
                "real_time_quotes": False,
                "advanced_data": False
            },
            "premium": {
                "max_subscriptions": 50,
                "update_frequency": "1s",
                "real_time_quotes": True,
                "advanced_data": True
            },
            "enterprise": {
                "max_subscriptions": 500,
                "update_frequency": "real-time",
                "real_time_quotes": True,
                "advanced_data": True,
                "custom_alerts": True
            }
        }
        return features.get(subscription_tier, features["free"])
    
    def _get_max_subscriptions(self, subscription_tier: str) -> int:
        """Get maximum subscriptions for tier"""
        limits = {"free": 5, "premium": 50, "enterprise": 500}
        return limits.get(subscription_tier, 5)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        return {
            "active_connections": len(self.active_connections),
            "total_subscriptions": sum(len(subs) for subs in self.symbol_subscriptions.values()),
            "unique_symbols": len(self.symbol_subscriptions),
            "connection_metadata": self.connection_metadata
        }


# Global connection manager instance
connection_manager = ConnectionManager()
