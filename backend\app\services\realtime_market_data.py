"""
Real-time market data service with WebSocket connections for live quotes,
options chains, and level 2 data
"""

import asyncio
import json
import logging
import websockets
from typing import Dict, List, Optional, Any, Set
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy import select, and_
import aioredis

from app.core.config import settings
from app.core.database import get_db
from app.models.stock import Stock
from app.models.options import OptionContract
from app.models.user import User
from app.services.data_provider import DataProvider

logger = logging.getLogger(__name__)

class ConnectionManager:
    """WebSocket connection manager for real-time data"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_subscriptions: Dict[int, Set[str]] = {}  # user_id -> set of symbols
        self.symbol_subscribers: Dict[str, Set[int]] = {}  # symbol -> set of user_ids
        self.redis_client: Optional[aioredis.Redis] = None
        
    async def initialize(self):
        """Initialize Redis connection for pub/sub"""
        try:
            self.redis_client = aioredis.from_url(
                f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}",
                decode_responses=True
            )
            logger.info("Redis connection initialized for real-time data")
        except Exception as e:
            logger.error(f"Failed to initialize Redis: {e}")
    
    async def connect(self, websocket: WebSocket, user_id: int):
        """Accept new WebSocket connection"""
        await websocket.accept()
        connection_id = f"user_{user_id}_{datetime.utcnow().timestamp()}"
        self.active_connections[connection_id] = websocket
        
        if user_id not in self.user_subscriptions:
            self.user_subscriptions[user_id] = set()
        
        logger.info(f"User {user_id} connected with connection {connection_id}")
        return connection_id
    
    async def disconnect(self, connection_id: str, user_id: int):
        """Handle WebSocket disconnection"""
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        
        # Clean up subscriptions if no more connections for user
        user_connections = [cid for cid in self.active_connections.keys() if cid.startswith(f"user_{user_id}_")]
        if not user_connections and user_id in self.user_subscriptions:
            # Remove user from all symbol subscriptions
            for symbol in self.user_subscriptions[user_id]:
                if symbol in self.symbol_subscribers:
                    self.symbol_subscribers[symbol].discard(user_id)
                    if not self.symbol_subscribers[symbol]:
                        del self.symbol_subscribers[symbol]
            
            del self.user_subscriptions[user_id]
        
        logger.info(f"User {user_id} disconnected from {connection_id}")
    
    async def subscribe_to_symbol(self, user_id: int, symbol: str):
        """Subscribe user to symbol updates"""
        if user_id not in self.user_subscriptions:
            self.user_subscriptions[user_id] = set()
        
        self.user_subscriptions[user_id].add(symbol)
        
        if symbol not in self.symbol_subscribers:
            self.symbol_subscribers[symbol] = set()
        
        self.symbol_subscribers[symbol].add(user_id)
        logger.info(f"User {user_id} subscribed to {symbol}")
    
    async def unsubscribe_from_symbol(self, user_id: int, symbol: str):
        """Unsubscribe user from symbol updates"""
        if user_id in self.user_subscriptions:
            self.user_subscriptions[user_id].discard(symbol)
        
        if symbol in self.symbol_subscribers:
            self.symbol_subscribers[symbol].discard(user_id)
            if not self.symbol_subscribers[symbol]:
                del self.symbol_subscribers[symbol]
        
        logger.info(f"User {user_id} unsubscribed from {symbol}")
    
    async def broadcast_to_symbol_subscribers(self, symbol: str, data: Dict[str, Any]):
        """Broadcast data to all subscribers of a symbol"""
        if symbol not in self.symbol_subscribers:
            return
        
        message = json.dumps({
            "type": "market_data",
            "symbol": symbol,
            "data": data,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        disconnected_connections = []
        
        for user_id in self.symbol_subscribers[symbol]:
            user_connections = [
                (cid, ws) for cid, ws in self.active_connections.items() 
                if cid.startswith(f"user_{user_id}_")
            ]
            
            for connection_id, websocket in user_connections:
                try:
                    await websocket.send_text(message)
                except Exception as e:
                    logger.error(f"Error sending to {connection_id}: {e}")
                    disconnected_connections.append((connection_id, user_id))
        
        # Clean up disconnected connections
        for connection_id, user_id in disconnected_connections:
            await self.disconnect(connection_id, user_id)

class RealTimeMarketDataService:
    """Real-time market data service"""
    
    def __init__(self):
        self.connection_manager = ConnectionManager()
        self.data_provider = DataProvider()
        self.is_running = False
        self.update_tasks: Dict[str, asyncio.Task] = {}
        
    async def start(self):
        """Start the real-time data service"""
        if self.is_running:
            return
        
        await self.connection_manager.initialize()
        self.is_running = True
        
        # Start background tasks
        asyncio.create_task(self._market_data_updater())
        asyncio.create_task(self._options_chain_updater())
        
        logger.info("Real-time market data service started")
    
    async def stop(self):
        """Stop the real-time data service"""
        self.is_running = False
        
        # Cancel all update tasks
        for task in self.update_tasks.values():
            task.cancel()
        
        logger.info("Real-time market data service stopped")
    
    async def handle_websocket_connection(self, websocket: WebSocket, user_id: int):
        """Handle WebSocket connection for real-time data"""
        connection_id = await self.connection_manager.connect(websocket, user_id)
        
        try:
            while True:
                # Receive messages from client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                await self._handle_client_message(user_id, message)
                
        except WebSocketDisconnect:
            await self.connection_manager.disconnect(connection_id, user_id)
        except Exception as e:
            logger.error(f"WebSocket error for user {user_id}: {e}")
            await self.connection_manager.disconnect(connection_id, user_id)
    
    async def _handle_client_message(self, user_id: int, message: Dict[str, Any]):
        """Handle incoming client messages"""
        try:
            message_type = message.get("type")
            
            if message_type == "subscribe":
                symbol = message.get("symbol")
                if symbol:
                    await self.connection_manager.subscribe_to_symbol(user_id, symbol)
                    await self._send_initial_data(user_id, symbol)
            
            elif message_type == "unsubscribe":
                symbol = message.get("symbol")
                if symbol:
                    await self.connection_manager.unsubscribe_from_symbol(user_id, symbol)
            
            elif message_type == "subscribe_options_chain":
                symbol = message.get("symbol")
                if symbol:
                    await self._send_options_chain(user_id, symbol)
            
            elif message_type == "subscribe_level2":
                symbol = message.get("symbol")
                if symbol:
                    await self._send_level2_data(user_id, symbol)
                    
        except Exception as e:
            logger.error(f"Error handling client message: {e}")
    
    async def _send_initial_data(self, user_id: int, symbol: str):
        """Send initial market data for a symbol"""
        try:
            # Get current stock data
            async with get_db() as db:
                result = await db.execute(
                    select(Stock).where(Stock.symbol == symbol)
                )
                stock = result.scalar_one_or_none()
                
                if stock:
                    initial_data = {
                        "type": "initial_data",
                        "symbol": symbol,
                        "data": {
                            "price": stock.current_price,
                            "change": stock.day_change,
                            "change_percent": stock.day_change_percent,
                            "volume": stock.volume,
                            "bid": stock.bid_price,
                            "ask": stock.ask_price,
                            "bid_size": stock.bid_size,
                            "ask_size": stock.ask_size
                        },
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    
                    # Send to specific user
                    user_connections = [
                        ws for cid, ws in self.connection_manager.active_connections.items()
                        if cid.startswith(f"user_{user_id}_")
                    ]
                    
                    for websocket in user_connections:
                        try:
                            await websocket.send_text(json.dumps(initial_data))
                        except Exception as e:
                            logger.error(f"Error sending initial data: {e}")
                            
        except Exception as e:
            logger.error(f"Error sending initial data for {symbol}: {e}")
    
    async def _send_options_chain(self, user_id: int, symbol: str):
        """Send options chain data for a symbol"""
        try:
            async with get_db() as db:
                # Get underlying stock
                result = await db.execute(
                    select(Stock).where(Stock.symbol == symbol)
                )
                stock = result.scalar_one_or_none()
                
                if not stock:
                    return
                
                # Get options contracts
                result = await db.execute(
                    select(OptionContract).where(
                        and_(
                            OptionContract.underlying_stock_id == stock.id,
                            OptionContract.expiration_date >= datetime.utcnow()
                        )
                    ).order_by(
                        OptionContract.expiration_date,
                        OptionContract.strike_price
                    )
                )
                options = result.scalars().all()
                
                # Group by expiration date
                options_chain = {}
                for option in options:
                    exp_date = option.expiration_date.date().isoformat()
                    if exp_date not in options_chain:
                        options_chain[exp_date] = {"calls": [], "puts": []}
                    
                    option_data = {
                        "strike": float(option.strike_price),
                        "last_price": float(option.last_price) if option.last_price else None,
                        "bid": float(option.bid_price) if option.bid_price else None,
                        "ask": float(option.ask_price) if option.ask_price else None,
                        "volume": option.volume,
                        "open_interest": option.open_interest,
                        "implied_volatility": float(option.implied_volatility) if option.implied_volatility else None,
                        "delta": float(option.delta) if option.delta else None,
                        "gamma": float(option.gamma) if option.gamma else None,
                        "theta": float(option.theta) if option.theta else None,
                        "vega": float(option.vega) if option.vega else None
                    }
                    
                    if option.option_type.value == "call":
                        options_chain[exp_date]["calls"].append(option_data)
                    else:
                        options_chain[exp_date]["puts"].append(option_data)
                
                # Send options chain data
                chain_data = {
                    "type": "options_chain",
                    "symbol": symbol,
                    "underlying_price": float(stock.current_price) if stock.current_price else None,
                    "options_chain": options_chain,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                # Send to specific user
                user_connections = [
                    ws for cid, ws in self.connection_manager.active_connections.items()
                    if cid.startswith(f"user_{user_id}_")
                ]
                
                for websocket in user_connections:
                    try:
                        await websocket.send_text(json.dumps(chain_data))
                    except Exception as e:
                        logger.error(f"Error sending options chain: {e}")
                        
        except Exception as e:
            logger.error(f"Error sending options chain for {symbol}: {e}")
    
    async def _send_level2_data(self, user_id: int, symbol: str):
        """Send Level 2 market data (order book)"""
        try:
            # Simulate Level 2 data (in production, this would come from market data feed)
            level2_data = {
                "type": "level2",
                "symbol": symbol,
                "bids": [
                    {"price": 100.50, "size": 1000, "orders": 5},
                    {"price": 100.49, "size": 2500, "orders": 12},
                    {"price": 100.48, "size": 1800, "orders": 8},
                    {"price": 100.47, "size": 3200, "orders": 15},
                    {"price": 100.46, "size": 900, "orders": 4}
                ],
                "asks": [
                    {"price": 100.51, "size": 800, "orders": 3},
                    {"price": 100.52, "size": 1500, "orders": 7},
                    {"price": 100.53, "size": 2200, "orders": 11},
                    {"price": 100.54, "size": 1200, "orders": 6},
                    {"price": 100.55, "size": 1900, "orders": 9}
                ],
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Send to specific user
            user_connections = [
                ws for cid, ws in self.connection_manager.active_connections.items()
                if cid.startswith(f"user_{user_id}_")
            ]
            
            for websocket in user_connections:
                try:
                    await websocket.send_text(json.dumps(level2_data))
                except Exception as e:
                    logger.error(f"Error sending Level 2 data: {e}")
                    
        except Exception as e:
            logger.error(f"Error sending Level 2 data for {symbol}: {e}")
    
    async def _market_data_updater(self):
        """Background task to update market data"""
        while self.is_running:
            try:
                # Get all subscribed symbols
                subscribed_symbols = set(self.connection_manager.symbol_subscribers.keys())
                
                for symbol in subscribed_symbols:
                    # Simulate market data update (in production, this would come from real feed)
                    updated_data = {
                        "price": 100.50 + (hash(symbol + str(datetime.utcnow().second)) % 100) / 100,
                        "volume": 1000000 + (hash(symbol) % 500000),
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    
                    await self.connection_manager.broadcast_to_symbol_subscribers(
                        symbol, updated_data
                    )
                
                await asyncio.sleep(1)  # Update every second
                
            except Exception as e:
                logger.error(f"Error in market data updater: {e}")
                await asyncio.sleep(5)
    
    async def _options_chain_updater(self):
        """Background task to update options chain data"""
        while self.is_running:
            try:
                # Update options data less frequently (every 30 seconds)
                await asyncio.sleep(30)
                
                # This would update options Greeks and prices in production
                logger.debug("Options chain update cycle completed")
                
            except Exception as e:
                logger.error(f"Error in options chain updater: {e}")
                await asyncio.sleep(60)

# Global instance
realtime_service = RealTimeMarketDataService()
